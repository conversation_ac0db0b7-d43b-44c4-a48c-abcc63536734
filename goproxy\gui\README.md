# Proxy-GUI
Based on the proxy platform SDK, the author and many enthusiasts have developed the GUI version of the proxy for each platform.

## Windows

- Official java version, Project Homepage:[goproxy-jui](https://github.com/snail007/goproxy-jui)
- Official C# version, Project Homepage:[gptool-csharp](https://github.com/snail007/gptool-csharp)

## Linux

- Official java version, Project Homepage:[goproxy-jui](https://github.com/snail007/goproxy-jui)

## MacOS

- Coming Soon ...

## Android

- goproxy official plugin of ss . Download address:[ss-plugin-goproxy](https://github.com/snail007/goproxy-ss-plugin-android)


## IOS

- Coming Soon ...

## Cross-platform

- proxy-web,A cross-platform web UI version,project home page:[proxy-web](https://github.com/yincongcyincong/proxy-web)