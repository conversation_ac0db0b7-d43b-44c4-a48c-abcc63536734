"""
统一常量定义
所有评分和测试相关常量的统一管理
"""

from environs import Env

# 初始化环境变量读取器
env = Env()

# ============================================================================
# 代理评分相关常量
# ============================================================================
# 支持从环境变量读取，提供默认值
PROXY_SCORE_MAX = env.int('PROXY_SCORE_MAX', 100)      # 代理最高分数（满分）
PROXY_SCORE_MIN = env.int('PROXY_SCORE_MIN', 0)        # 代理最低分数（淘汰线）
PROXY_SCORE_INIT = env.int('PROXY_SCORE_INIT', 10)     # 代理初始分数
PROXY_SCORE_DECREASE = env.int('PROXY_SCORE_DECREASE', 1)  # 测试失败时减少的分数

# ============================================================================
# 测试相关常量
# ============================================================================
# 默认超时时间
DEFAULT_TEST_TIMEOUT = 10

# 默认有效状态码
DEFAULT_VALID_STATUS_CODES = [200, 302]

# 默认批处理大小
DEFAULT_BATCH_SIZE = 50

# 默认最大并发数
DEFAULT_MAX_CONCURRENT = 20

# ============================================================================
# 日志格式常量
# ============================================================================
# 统一的日志格式
LOG_FORMAT_SIMPLE = "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name} | {message}"
LOG_FORMAT_DETAILED = "{time:YYYY-MM-DD HH:mm:ss.SSS} | {level:<8} | {name:<20} | {function}:{line} | {message}"

# 错误日志格式
ERROR_LOG_FORMAT = "{time:YYYY-MM-DD HH:mm:ss} | ERROR | {name} | {function}:{line} | {message} | {exception}"

# ============================================================================
# 错误消息模板
# ============================================================================
# 统一的错误消息模板
ERROR_MSG_PROXY_TEST_FAILED = "代理测试失败: {address} -> {error}"
ERROR_MSG_PROXY_TEST_EXCEPTION = "代理测试异常: {address} -> {error}"
ERROR_MSG_SCORE_UPDATE_FAILED = "更新代理分数失败: {address} -> {error}"
ERROR_MSG_BATCH_TEST_FAILED = "批量测试失败: {error}"
ERROR_MSG_CONFIG_INVALID = "配置无效: {config} -> {error}"

# 信息消息模板
INFO_MSG_PROXY_TEST_SUCCESS = "代理测试成功，设置满分: {address}"
INFO_MSG_PROXY_TEST_SUCCESS_NO_SCORE = "代理测试成功，但配置不设置满分: {address}"
INFO_MSG_PROXY_TEST_FAILED = "代理测试失败，降低分数: {address} -> {error}"
INFO_MSG_BATCH_TEST_START = "开始批量测试 {count} 个代理"
INFO_MSG_BATCH_TEST_COMPLETE = "批量测试完成: 成功 {success}, 失败 {failed}, 异常 {error}"

# 警告消息模板
WARNING_MSG_TESTER_NOT_SET = "代理测试器未设置，无法进行测试"
WARNING_MSG_UNSUPPORTED_PROTOCOL = "不支持的协议: {protocol}"
WARNING_MSG_INVALID_PROXY = "无效的代理: {proxy}"