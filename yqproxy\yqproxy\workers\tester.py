#!/usr/bin/env python3
"""
代理测试器工作进程
基于services层重构的代理测试逻辑，独立可运行
"""

import asyncio
import sys
from typing import Dict, List, Tuple
from tqdm import tqdm

from yqproxy.services import create_integrated_services
from yqproxy.models import SUPPORTED_PROTOCOLS
from yqproxy.utils.logger import get_logger
from yqproxy.config import TestConfig, get_test_config


class ProxyTesterWorker:
    """代理测试器工作进程 - 只负责调度，不实现业务逻辑"""
    
    def __init__(self):
        self.proxy_manager = None
        self.proxy_validator = None  # 使用ProxyValidator替代直接的proxy_tester
        self.running = False
        self.test_stats = {}
        self.logger = get_logger(__name__)
        
    async def initialize(self):
        """初始化服务依赖"""
        try:
            # 使用统一的测试配置
            test_config = get_test_config()
            
            self.logger.info(f"初始化测试配置: {test_config}")
            
            # 创建集成服务
            _, proxy_tester, self.proxy_manager = await create_integrated_services(
                redis_client=None,  # 使用默认 Redis 客户端
                test_config=test_config
            )
            
            # 获取ProxyValidator实例（统一的测试和评分逻辑）
            self.proxy_validator = self.proxy_manager.validator
            
            # 验证依赖注入是否正确
            if self.proxy_validator is None:
                raise RuntimeError("ProxyValidator实例获取失败")
            
            if self.proxy_validator.proxy_tester is None:
                raise RuntimeError("ProxyValidator未正确获得ProxyTester依赖")
            
            if self.proxy_validator.proxy_pool is None:
                raise RuntimeError("ProxyValidator未正确获得ProxyPool依赖")
            
            # 验证ProxyManager的其他依赖
            if self.proxy_manager.proxy_pool is None:
                raise RuntimeError("ProxyManager未正确获得ProxyPool依赖")
            
            self.logger.info(
                f"代理测试器初始化完成，配置："
                f"批量{test_config.batch_size}个，并发{test_config.max_concurrent}个"
            )
            self.logger.debug(f"ProxyValidator依赖验证通过: ProxyTester={self.proxy_validator.proxy_tester is not None}, ProxyPool={self.proxy_validator.proxy_pool is not None}")
            
        except Exception as e:
            self.logger.error(f"代理测试器初始化失败: {e}")
            # 清理已创建的资源
            await self.cleanup()
            raise
    
    async def test_single_proxy(self, proxy) -> bool:
        """测试单个代理并更新分数（委托给ProxyValidator）"""
        if not self.proxy_validator:
            self.logger.error("ProxyValidator未初始化")
            return False
        
        try:
            # 完全委托给ProxyValidator处理测试和评分逻辑
            return await self.proxy_validator.test_and_score_single(proxy)
            
        except Exception as e:
            self.logger.error(f"测试代理失败: {proxy.address} -> {e}")
            return False
    
    async def test_batch(self, proxies: List) -> Tuple[int, int]:
        """测试一批代理，返回(成功数, 失败数)（委托给ProxyValidator）"""
        if not self.proxy_validator:
            self.logger.error("ProxyValidator未初始化")
            return 0, len(proxies)
        
        try:
            # 完全委托给ProxyValidator的批量测试方法
            return await self.proxy_validator.get_batch_results_summary(proxies)
        except Exception as e:
            self.logger.error(f"批量测试失败: {e}")
            return 0, len(proxies)
    
    async def test_protocol(self, protocol: str, pbar: tqdm = None) -> Dict:
        """测试单个协议的所有代理"""
        proxies = await self.proxy_manager.proxy_pool.get_all_proxies(protocol)
        if not proxies:
            return {'success': 0, 'failed': 0, 'total': 0}
        
        success_total = failed_total = 0
        
        # 分批测试
        config = get_test_config()
        for i in range(0, len(proxies), config.batch_size):
            if not self.running:
                break
                
            batch = proxies[i:i + config.batch_size]
            success, failed = await self.test_batch(batch)
            
            success_total += success
            failed_total += failed
            
            if pbar:
                pbar.update(len(batch))
                pbar.set_postfix({'success': success_total, 'failed': failed_total})
        
        return {
            'success': success_total,
            'failed': failed_total,
            'total': success_total + failed_total
        }
    
    async def get_active_protocols(self) -> List[Tuple[str, int]]:
        """获取有代理的协议列表，返回 [(protocol, count), ...]"""
        stats = await self.proxy_manager.get_statistics()
        protocols = []
        
        for protocol in SUPPORTED_PROTOCOLS:
            count = stats.get('protocols', {}).get(protocol, {}).get('count', 0)
            if count > 0:
                protocols.append((protocol, count))
        
        return protocols
    
    async def run_test_cycle(self):
        """运行一轮测试"""
        self.running = True
        
        # 获取活动协议
        protocols = await self.get_active_protocols()
        if not protocols:
            self.logger.info('没有代理需要测试')
            return
        
        config = get_test_config()
        self.logger.info(f'当前测试模式：{config.test_url_mode}，测试URL：{config.test_url}')
        print(f'{"="*20} 当前测试模式：{config.test_url_mode} {"="*20}\n')
        
        # 创建进度条
        progress_bars = {
            protocol: tqdm(
                total=count,
                desc=f'{protocol.upper():>7}',
                unit='代理',
                position=idx,
                leave=True,
                ncols=100
            )
            for idx, (protocol, count) in enumerate(protocols)
        }
        
        # 并行测试所有协议
        tasks = [
            self.test_protocol(protocol, progress_bars[protocol])
            for protocol, _ in protocols
        ]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 更新统计
        for (protocol, _), result in zip(protocols, results):
            if isinstance(result, dict):
                self.test_stats[protocol] = result
        
        # 清理进度条
        for pbar in progress_bars.values():
            pbar.close()
        print('\n' * len(progress_bars))
        
        # 输出总结
        total_success = sum(s.get('success', 0) for s in self.test_stats.values())
        total_tested = sum(s.get('total', 0) for s in self.test_stats.values())
        success_rate = round(total_success / total_tested * 100, 2) if total_tested > 0 else 0
        
        self.logger.info(f'测试完成: 总共 {total_tested} 个，成功 {total_success} 个，成功率 {success_rate}%')
    
    async def run_once(self):
        """
        运行一次代理测试任务（与 ProxyGetter 保持一致的接口）
        
        Returns:
            bool: 是否成功执行测试
        """
        # 确保已初始化
        if not self.proxy_manager:
            await self.initialize()
        
        try:
            self.test_stats = {}
            await self.run_test_cycle()
            return True
        except Exception as e:
            self.logger.error(f"执行代理测试任务失败: {e}")
            return False
    
    async def run(self):
        """运行一次代理测试任务"""
        await self.initialize()
        try:
            self.test_stats = {}
            await self.run_test_cycle()
        finally:
            await self.cleanup()
    
    async def cleanup(self):
        """清理资源"""
        self.running = False
        
        try:
            if self.proxy_manager:
                await self.proxy_manager.close()
                self.logger.debug("ProxyManager资源已清理")
            
            # 清理引用
            self.proxy_validator = None
            self.proxy_manager = None
            
            self.logger.info("代理测试器资源清理完成")
            
        except Exception as e:
            self.logger.error(f"资源清理过程中发生错误: {e}")
            # 即使清理失败，也要重置引用
            self.proxy_validator = None
            self.proxy_manager = None


async def main():
    """主函数"""
    tester = ProxyTesterWorker()
    logger = get_logger(__name__)
    
    try:
        await tester.run()
    except KeyboardInterrupt:
        logger.info("用户中断，正在停止...")
        await tester.cleanup()
    except Exception as e:
        logger.error(f"代理测试器运行失败: {e}")
        await tester.cleanup()
        sys.exit(1)


if __name__ == '__main__':
    # 使用项目统一的日志配置
    asyncio.run(main())