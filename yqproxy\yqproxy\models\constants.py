"""
数据模型常量定义
纯常量，无任何依赖
"""
from environs import Env

# 初始化环境变量读取器
env = Env()

class Protocol:
    """代理协议类型常量"""
    HTTP = "http"
    HTTPS = "https"
    SOCKS4 = "socks4"
    SOCKS5 = "socks5"


# 支持的协议列表
SUPPORTED_PROTOCOLS = [
    Protocol.HTTP,
    Protocol.HTTPS,
    Protocol.SOCKS4,
    Protocol.SOCKS5
]

# 安全协议列表
SECURE_PROTOCOLS = [
    Protocol.HTTPS,
    Protocol.SOCKS5
]

# 默认协议
DEFAULT_PROTOCOL = Protocol.HTTP

# 代理评分相关常量 - 从统一配置导入
from yqproxy.config.constants import (
    PROXY_SCORE_MAX,
    PROXY_SCORE_MIN,
    PROXY_SCORE_INIT,
    PROXY_SCORE_DECREASE
)