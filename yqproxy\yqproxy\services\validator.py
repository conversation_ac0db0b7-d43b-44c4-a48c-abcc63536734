"""
代理验证器模块
负责代理的测试和验证相关功能
统一的测试和评分逻辑，避免重复实现
"""

from typing import Dict, List, Tuple
from yqproxy.utils import get_logger
from yqproxy.models import Proxy
from yqproxy.config.constants import (
    PROXY_SCORE_DECREASE,
    ERROR_MSG_PROXY_TEST_FAILED,
    ERROR_MSG_PROXY_TEST_EXCEPTION,
    ERROR_MSG_SCORE_UPDATE_FAILED,
    ERROR_MSG_BATCH_TEST_FAILED,
    INFO_MSG_PROXY_TEST_SUCCESS,
    INFO_MSG_PROXY_TEST_SUCCESS_NO_SCORE,
    INFO_MSG_PROXY_TEST_FAILED,
    INFO_MSG_BATCH_TEST_START,
    INFO_MSG_BATCH_TEST_COMPLETE,
    WARNING_MSG_TESTER_NOT_SET
)
from yqproxy.config import get_test_config


class ProxyValidator:
    """
    代理验证器
    负责代理的测试、验证和分数更新
    统一的业务逻辑层，组合ProxyTester和ProxyPool
    """
    
    def __init__(self, proxy_pool, proxy_tester=None):
        """
        初始化代理验证器
        
        Args:
            proxy_pool: 代理池实例
            proxy_tester: 代理测试器实例
        """
        self.proxy_pool = proxy_pool
        self.proxy_tester = proxy_tester
        self.logger = get_logger(self.__class__.__name__)
        self.config = get_test_config()  # 使用统一配置
        
        if not proxy_tester:
            self.logger.warning(WARNING_MSG_TESTER_NOT_SET)
    
    def set_proxy_tester(self, tester):
        """设置代理测试器"""
        self.proxy_tester = tester
        if tester:
            self.logger.debug("ProxyTester已设置到ProxyValidator")
    
    async def test_proxy(self, proxy: Proxy) -> bool:
        """
        测试单个代理（纯测试，不更新分数）
        
        Args:
            proxy: 代理对象
            
        Returns:
            测试是否成功
        """
        if not self.proxy_tester:
            self.logger.warning(WARNING_MSG_TESTER_NOT_SET)
            return False
        
        try:
            result = await self.proxy_tester.test_single_proxy(proxy)
            return result.success
        except Exception as e:
            self.logger.error(ERROR_MSG_PROXY_TEST_FAILED.format(address=proxy.address, error=e))
            return False
    
    async def test_and_update_proxy(self, proxy: Proxy) -> bool:
        """
        测试代理并更新分数（向后兼容方法）
        
        Args:
            proxy: 代理对象
            
        Returns:
            测试是否成功
        """
        return await self.test_and_score_single(proxy)
    
    async def test_and_score_single(self, proxy: Proxy) -> bool:
        """
        测试单个代理并更新分数（统一接口）
        
        Args:
            proxy: 代理对象
            
        Returns:
            测试是否成功
        """
        if not self.proxy_tester:
            self.logger.warning(WARNING_MSG_TESTER_NOT_SET)
            return False
        
        try:
            # 使用ProxyTester进行实际测试
            result = await self.proxy_tester.test_single_proxy(proxy)
            
            # 根据测试结果更新分数
            if result.success:
                if not self.config.dont_set_max_score:
                    await self.proxy_pool.set_max_score(proxy)
                    self.logger.debug(INFO_MSG_PROXY_TEST_SUCCESS.format(address=proxy.address))
                else:
                    self.logger.debug(INFO_MSG_PROXY_TEST_SUCCESS_NO_SCORE.format(address=proxy.address))
            else:
                await self.proxy_pool.decrease_score(proxy, PROXY_SCORE_DECREASE)
                self.logger.debug(INFO_MSG_PROXY_TEST_FAILED.format(
                    address=proxy.address, 
                    error=result.error or '未知错误'
                ))
            
            return result.success
            
        except Exception as e:
            # 异常情况也要降低分数
            self.logger.error(ERROR_MSG_PROXY_TEST_EXCEPTION.format(address=proxy.address, error=e))
            try:
                await self.proxy_pool.decrease_score(proxy, PROXY_SCORE_DECREASE)
            except Exception as score_error:
                self.logger.error(ERROR_MSG_SCORE_UPDATE_FAILED.format(
                    address=proxy.address, 
                    error=score_error
                ))
            return False

    async def test_proxies_batch(self, proxies: List[Proxy]) -> Dict[str, bool]:
        """
        批量测试代理（纯测试，不更新分数，向后兼容）
        
        Args:
            proxies: 代理列表
            
        Returns:
            测试结果 {address: success}
        """
        if not self.proxy_tester:
            self.logger.warning(WARNING_MSG_TESTER_NOT_SET)
            return {}
        
        try:
            results = await self.proxy_tester.test_proxy_batch(proxies)
            return {result.proxy.address: result.success for result in results}
        except Exception as e:
            self.logger.error(ERROR_MSG_BATCH_TEST_FAILED.format(error=e))
            return {}
    
    async def test_and_score_batch(self, proxies: List[Proxy]) -> Dict[str, int]:
        """
        批量测试代理并更新分数（统一接口）
        
        Args:
            proxies: 代理列表
            
        Returns:
            测试结果统计 {'success': count, 'failed': count, 'error': count}
        """
        if not proxies:
            return {'success': 0, 'failed': 0, 'error': 0}
        
        if not self.proxy_tester:
            self.logger.warning(WARNING_MSG_TESTER_NOT_SET)
            return {'success': 0, 'failed': 0, 'error': len(proxies)}
        
        success_count = 0
        failed_count = 0
        error_count = 0
        
        try:
            self.logger.info(INFO_MSG_BATCH_TEST_START.format(count=len(proxies)))
            
            # 使用ProxyTester进行批量测试
            results = await self.proxy_tester.test_proxy_batch(proxies)
            
            # 处理每个测试结果并更新分数
            for result in results:
                try:
                    if result.success:
                        if not self.config.dont_set_max_score:
                            await self.proxy_pool.set_max_score(result.proxy)
                            self.logger.debug(INFO_MSG_PROXY_TEST_SUCCESS.format(address=result.proxy.address))
                        else:
                            self.logger.debug(INFO_MSG_PROXY_TEST_SUCCESS_NO_SCORE.format(address=result.proxy.address))
                        success_count += 1
                    else:
                        await self.proxy_pool.decrease_score(result.proxy, PROXY_SCORE_DECREASE)
                        failed_count += 1
                        self.logger.debug(INFO_MSG_PROXY_TEST_FAILED.format(
                            address=result.proxy.address, 
                            error=result.error or '未知错误'
                        ))
                        
                except Exception as score_error:
                    self.logger.error(ERROR_MSG_SCORE_UPDATE_FAILED.format(
                        address=result.proxy.address, 
                        error=score_error
                    ))
                    error_count += 1
            
            self.logger.info(INFO_MSG_BATCH_TEST_COMPLETE.format(
                success=success_count, 
                failed=failed_count, 
                error=error_count
            ))
            
            return {
                'success': success_count,
                'failed': failed_count,
                'error': error_count
            }
            
        except Exception as e:
            self.logger.error(ERROR_MSG_BATCH_TEST_FAILED.format(error=e))
            return {
                'success': success_count,
                'failed': failed_count,
                'error': len(proxies) - success_count - failed_count
            }
    
    async def validate_and_score_batch(self, proxies: List[Proxy]) -> Dict[str, int]:
        """
        批量验证代理并更新分数（向后兼容方法）
        
        Args:
            proxies: 代理列表
            
        Returns:
            验证结果统计 {status: count}
        """
        return await self.test_and_score_batch(proxies)
    
    async def get_batch_results_summary(self, proxies: List[Proxy]) -> Tuple[int, int]:
        """
        获取批量测试结果摘要（为ProxyTesterWorker提供的便利方法）
        
        Args:
            proxies: 代理列表
            
        Returns:
            (成功数, 失败数) 元组
        """
        results = await self.test_and_score_batch(proxies)
        success = results.get('success', 0)
        failed = results.get('failed', 0) + results.get('error', 0)  # 将错误也算作失败
        return success, failed