<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
<meta name="pinterest" content="nopin">
<meta name="viewport" content="width=device-width,minimum-scale=1,initial-scale=1">
<meta name="generator" content="Hugo 0.56.3" />

<link rel="alternate" type="application/rss+xml" title="RSS" href="https://snail007.goproxyauth.com/goproxy/categories/%E6%9E%B6%E6%9E%84%E8%A7%A3%E8%AF%B4/index.xml">

<link rel="canonical" href="https://snail007.goproxyauth.com/goproxy/categories/%E6%9E%B6%E6%9E%84%E8%A7%A3%E8%AF%B4/">


    <link href="//maxcdn.bootstrapcdn.com/font-awesome/4.5.0/css/font-awesome.min.css" rel="stylesheet">
    <link rel="stylesheet" href="//maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css">
    <link rel="stylesheet" href="//cdnjs.cloudflare.com/ajax/libs/highlight.js/8.4/styles/solarized_dark.min.css">
    <title>架构解说 - GOPROXY IN ACTION</title>
    
    <link href="https://snail007.goproxyauth.com/goproxy/css/styles.css" rel="stylesheet">
    

  </head>

  <body>
    
    
    

    <header class="l-header">
      <nav class="navbar navbar-default">
        <div class="container">
          <div class="navbar-header">
            <button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#navbar" aria-expanded="false">
              <span class="sr-only">Toggle navigation</span>
              <span class="icon-bar"></span>
              <span class="icon-bar"></span>
              <span class="icon-bar"></span>
            </button>
            <a class="navbar-brand" href="https://snail007.goproxyauth.com/goproxy/">GOPROXY IN ACTION</a>
          </div>

          
          <div id="navbar" class="collapse navbar-collapse">
            
            <ul class="nav navbar-nav navbar-right">
              
              
              <li><a href="https://github.com/snail007/goproxy/releases/">下载</a></li>
              
              
              
              <li><a href="https://snail007.goproxyauth.com/goproxy/categories/goproxy%E6%89%8B%E5%86%8C/">实战</a></li>
              
              
              
              <li><a href="https://snail007.goproxyauth.com/goproxy/manual/zh/#/">手册</a></li>
              
              
              
              <li><a href="/goproxy/about/">关于</a></li>
              
              
            </ul>
            
          </div>
          

        </div>
      </nav>
    </header>

    <main>
      <div class="container">
        
<div class="row">
  <div class="col-md-8">

    
    <header class="page-header">
      <h1>架构解说</h1>
    </header>
    

    <ul class="p-articles">
      
      <li><article class="li">
  <header>
    <ul class="p-facts">
      <li><i class="fa fa-calendar" aria-hidden="true"></i><time datetime="2019-06-14T15:29:47JST">Jun 14, 2019</time></li>
      <li><i class="fa fa-bookmark" aria-hidden="true"></i><a href="https://snail007.goproxyauth.com/goproxy/page/">page</a></li>
      
    </ul>
    <h2 class="title"><a href="https://snail007.goproxyauth.com/goproxy/page/categories/">Categories</a></h2>
  </header>

  
  <div class="summary">不用看这里，没什么内容，仅仅为了定义目录和tags。</div>

  
</article>
</li>
      
    </ul>

    


  </div>
  <div class="col-md-4">
    
<aside class="l-sidebar">

  <section class="panel panel-default">
    <div class="panel-heading">
      <div class="panel-title">LATESTS</div>
    </div>
    <div class="list-group">
      
      <a href="https://snail007.goproxyauth.com/goproxy/usage/first/" class="list-group-item">手册前言（使用必读）</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/page/free_vs_commercial/" class="list-group-item">GOPROXY免费版VS商业版</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/page/faq/goproxy%E5%B8%B8%E8%A7%81%E9%97%AE%E9%A2%98%E8%A7%A3%E7%AD%94/" class="list-group-item">常见问题解答</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/posts/domain-cf/" class="list-group-item">域名解析 域名控制台&#43;极客&#43;cloudflare 的配置方法</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/posts/windows-global-proxy-using-dns/" class="list-group-item">在windows下实现基于dns的自动全局代理</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/usage/tcp/" class="list-group-item">TCP代理</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/posts/cloudflare/" class="list-group-item">Cloudflare使用简明教程</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/posts/http-nat-cdn/" class="list-group-item">内网穿透也能用CDN？？？！</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/page/free_vs_commercial_en/" class="list-group-item">GOPROXY Free Version VS Commercial Version</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/posts/http_cdn_ws/" class="list-group-item">代理之套用CDN</a>
      
    </div>
  </section>

  
  <section class="panel panel-default">
    <div class="panel-heading">
      <div class="panel-title">CATEGORY</div>
    </div>
    <div class="list-group">
      
      <a href="https://snail007.goproxyauth.com/goproxy/categories/%E9%BB%98%E8%AE%A4%E5%88%86%E7%B1%BB" class="list-group-item">默认分类</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/categories/goproxy%E6%89%8B%E5%86%8C" class="list-group-item">goproxy手册</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/categories/%E6%9E%B6%E6%9E%84%E8%A7%A3%E8%AF%B4" class="list-group-item">架构解说</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/categories/%E7%BB%86%E8%AF%B4%E5%B1%82%E7%BA%A7" class="list-group-item">细说层级</a>
      
    </div>
  </section>
  
  <section class="panel panel-default">
    <div class="panel-heading">
      <div class="panel-title">TAG</div>
    </div>
    <div class="list-group">
      
      <a href="https://snail007.goproxyauth.com/goproxy/tags/cdn" class="list-group-item">cdn</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/tags/ws" class="list-group-item">ws</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/tags/cloudflare" class="list-group-item">cloudflare</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/tags/%E5%95%86%E4%B8%9A%E7%89%88" class="list-group-item">商业版</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/tags/commercial" class="list-group-item">commercial</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/tags/domain" class="list-group-item">domain</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/tags/tcp" class="list-group-item">tcp</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/tags/%E5%85%A8%E5%B1%80%E4%BB%A3%E7%90%86" class="list-group-item">全局代理</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/tags/%E5%86%85%E7%BD%91%E7%A9%BF%E9%80%8F" class="list-group-item">内网穿透</a>
      
    </div>
  </section>
  

</aside>


  </div>
</div>

      </div>
    </main>

    <footer class="l-footer">
      <div class="container">
        <p><span class="h-logo">&copy; GOPROXY IN ACTION</span></p>
      </div>
    </footer>

    <script src="//code.jquery.com/jquery-3.1.1.min.js"></script>
    <script src="//maxcdn.bootstrapcdn.com/bootstrap/3.3.7/js/bootstrap.min.js"></script>
    <script src="//cdnjs.cloudflare.com/ajax/libs/highlight.js/8.4/highlight.min.js"></script>
    <script>hljs.initHighlightingOnLoad();</script>
    <script>
    $(function(){
      $("img[css]").each(function(){
        $(this).attr("style",$(this).attr("css"));
      });
    });
    </script>
  </body>
</html>

