<?xml version="1.0" encoding="utf-8" standalone="yes" ?>
<rss version="2.0" xmlns:atom="http://www.w3.org/2005/Atom">
  <channel>
    <title>tcp on GOPROXY IN ACTION</title>
    <link>https://snail007.goproxyauth.com/goproxy/tags/tcp/</link>
    <description>Recent content in tcp on GOPROXY IN ACTION</description>
    <generator>Hugo -- gohugo.io</generator>
    <language>zh-CN</language>
    <lastBuildDate>Thu, 04 Jul 2019 11:00:50 +0800</lastBuildDate>
    
	<atom:link href="https://snail007.goproxyauth.com/goproxy/tags/tcp/index.xml" rel="self" type="application/rss+xml" />
    
    
    <item>
      <title>TCP代理</title>
      <link>https://snail007.goproxyauth.com/goproxy/usage/tcp/</link>
      <pubDate>Thu, 04 Jul 2019 11:00:50 +0800</pubDate>
      
      <guid>https://snail007.goproxyauth.com/goproxy/usage/tcp/</guid>
      <description>工作原理 tcp代理，也称为tcp端口转发，工作原理就是，把proxy监听端口进来的tcp连接的数据原样转发到上级监听的一个端口。 上级可以是p</description>
    </item>
    
  </channel>
</rss>