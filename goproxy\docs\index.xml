<?xml version="1.0" encoding="utf-8" standalone="yes" ?>
<rss version="2.0" xmlns:atom="http://www.w3.org/2005/Atom">
  <channel>
    <title>GOPROXY IN ACTION</title>
    <link>https://snail007.goproxyauth.com/goproxy/</link>
    <description>Recent content on GOPROXY IN ACTION</description>
    <generator>Hugo -- gohugo.io</generator>
    <language>zh-CN</language>
    <lastBuildDate>Fri, 05 Jul 2019 08:20:48 +0800</lastBuildDate>
    
	<atom:link href="https://snail007.goproxyauth.com/goproxy/index.xml" rel="self" type="application/rss+xml" />
    
    
    <item>
      <title>手册前言（使用必读）</title>
      <link>https://snail007.goproxyauth.com/goproxy/usage/first/</link>
      <pubDate>Thu, 04 Jul 2019 11:00:50 +0800</pubDate>
      
      <guid>https://snail007.goproxyauth.com/goproxy/usage/first/</guid>
      <description>前言 GOPROXY使用必读 手册和项目中使用了一部分的名词，它们本身的含义和在goproxy的专有含义是不一样的，所以前言中对这些名词做了解释</description>
    </item>
    
    <item>
      <title>GOPROXY免费版VS商业版</title>
      <link>https://snail007.goproxyauth.com/goproxy/page/free_vs_commercial/</link>
      <pubDate>Wed, 26 Jun 2019 13:19:15 +0800</pubDate>
      
      <guid>https://snail007.goproxyauth.com/goproxy/page/free_vs_commercial/</guid>
      <description>目前，GOPROXY 和 SDK 提供免费版和商业版，功能对比如下。 免费版 商业版 TCP转发 √ √ UDP转发 √ √ HTTP(S)代理 √ √ Socks5代理 √ √</description>
    </item>
    
    <item>
      <title>常见问题解答</title>
      <link>https://snail007.goproxyauth.com/goproxy/page/faq/goproxy%E5%B8%B8%E8%A7%81%E9%97%AE%E9%A2%98%E8%A7%A3%E7%AD%94/</link>
      <pubDate>Fri, 14 Jun 2019 16:29:13 +0800</pubDate>
      
      <guid>https://snail007.goproxyauth.com/goproxy/page/faq/goproxy%E5%B8%B8%E8%A7%81%E9%97%AE%E9%A2%98%E8%A7%A3%E7%AD%94/</guid>
      <description>问：http代理支持https网站吗？ 答：goproxy的http代理同时支持http和https网站。 问：socks5代理支持UDP吗？ 答</description>
    </item>
    
    <item>
      <title>域名解析 域名控制台&#43;极客&#43;cloudflare 的配置方法</title>
      <link>https://snail007.goproxyauth.com/goproxy/posts/domain-cf/</link>
      <pubDate>Fri, 05 Jul 2019 08:20:48 +0800</pubDate>
      
      <guid>https://snail007.goproxyauth.com/goproxy/posts/domain-cf/</guid>
      <description>准备工作 本文综合的介绍了cloudflare注册使用，从域名解析到回源，完成了一个二级域使用cloudflare的CDN加速的配置过程。 首先</description>
    </item>
    
    <item>
      <title>在windows下实现基于dns的自动全局代理</title>
      <link>https://snail007.goproxyauth.com/goproxy/posts/windows-global-proxy-using-dns/</link>
      <pubDate>Thu, 04 Jul 2019 17:54:31 +0800</pubDate>
      
      <guid>https://snail007.goproxyauth.com/goproxy/posts/windows-global-proxy-using-dns/</guid>
      <description>在windows下实现基于dns的自动全局代理。 本方法需要监听本地80和443端口，所以如果你有程序占用了这两个端口，请关闭相应的程序。 本文</description>
    </item>
    
    <item>
      <title>TCP代理</title>
      <link>https://snail007.goproxyauth.com/goproxy/usage/tcp/</link>
      <pubDate>Thu, 04 Jul 2019 11:00:50 +0800</pubDate>
      
      <guid>https://snail007.goproxyauth.com/goproxy/usage/tcp/</guid>
      <description>工作原理 tcp代理，也称为tcp端口转发，工作原理就是，把proxy监听端口进来的tcp连接的数据原样转发到上级监听的一个端口。 上级可以是p</description>
    </item>
    
    <item>
      <title>Cloudflare使用简明教程</title>
      <link>https://snail007.goproxyauth.com/goproxy/posts/cloudflare/</link>
      <pubDate>Fri, 28 Jun 2019 17:37:30 +0800</pubDate>
      
      <guid>https://snail007.goproxyauth.com/goproxy/posts/cloudflare/</guid>
      <description>前言 GOPROXY底层传输协议支持ws，可以利用cdn加速，使用cdn需要域名解析知识以及cdn的一些知识，那么很多小伙伴对cdn不是很熟悉</description>
    </item>
    
    <item>
      <title>内网穿透也能用CDN？？？！</title>
      <link>https://snail007.goproxyauth.com/goproxy/posts/http-nat-cdn/</link>
      <pubDate>Fri, 28 Jun 2019 17:02:57 +0800</pubDate>
      
      <guid>https://snail007.goproxyauth.com/goproxy/posts/http-nat-cdn/</guid>
      <description>内网穿透之套CDN 好处就不用说了吧，两个字稳！ 假如在windows上配置了可以访问的连接 http://127.0.0.1:801/abc/index.php , 如何能使用VPS+CDN做内网穿透呢？ 配置如下：</description>
    </item>
    
    <item>
      <title>GOPROXY Free Version VS Commercial Version</title>
      <link>https://snail007.goproxyauth.com/goproxy/page/free_vs_commercial_en/</link>
      <pubDate>Wed, 26 Jun 2019 13:19:15 +0800</pubDate>
      
      <guid>https://snail007.goproxyauth.com/goproxy/page/free_vs_commercial_en/</guid>
      <description>Currently, GOPROXY and SDK are available in both free and commercial versions. Function Free |Commercial TCP forwarding √ √ UDP forwarding √ √ HTTP(S)Agent √ √ Socks5 Agent √ √ Socks5 proxy UDP support √ √ SPS Integrated Agent √ √ SPS superior certification √ √ Intranet penetration √ √ DNS proxy, anti-pollution, independent DNS service √ √ HTTP(S) proxy authentication, authentication API, load balancing × √ SOCKS5 proxy authentication,</description>
    </item>
    
    <item>
      <title>代理之套用CDN</title>
      <link>https://snail007.goproxyauth.com/goproxy/posts/http_cdn_ws/</link>
      <pubDate>Fri, 14 Jun 2019 16:25:06 +0800</pubDate>
      
      <guid>https://snail007.goproxyauth.com/goproxy/posts/http_cdn_ws/</guid>
      <description>goproxy代理之套用CDN 准备 在VPS上下载对应平台的执行文件，这里以Linux为例。 在电脑上下载对应平台的执行文件，这里以Window</description>
    </item>
    
    <item>
      <title>Categories</title>
      <link>https://snail007.goproxyauth.com/goproxy/page/categories/</link>
      <pubDate>Fri, 14 Jun 2019 15:29:47 +0800</pubDate>
      
      <guid>https://snail007.goproxyauth.com/goproxy/page/categories/</guid>
      <description>不用看这里，没什么内容，仅仅为了定义目录和tags。</description>
    </item>
    
    <item>
      <title>About</title>
      <link>https://snail007.goproxyauth.com/goproxy/page/about/</link>
      <pubDate>Fri, 14 Jun 2019 13:22:19 +0800</pubDate>
      
      <guid>https://snail007.goproxyauth.com/goproxy/page/about/</guid>
      <description>It&amp;rsquo;s YOU!</description>
    </item>
    
  </channel>
</rss>