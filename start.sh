#!/bin/bash

# 代理池负载均衡系统启动脚本
# 使用方法: ./start.sh [选项]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker和Docker Compose
check_dependencies() {
    print_info "检查依赖..."
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        print_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    print_success "依赖检查通过"
}

# 检查配置文件
check_config() {
    print_info "检查配置文件..."
    
    if [ ! -f "yqproxy/.env" ]; then
        print_warning "yqproxy/.env 文件不存在，将从示例文件创建"
        if [ -f "yqproxy/.env.example" ]; then
            cp yqproxy/.env.example yqproxy/.env
            print_info "请编辑 yqproxy/.env 文件配置Redis连接信息"
        else
            print_error "yqproxy/.env.example 文件不存在"
            exit 1
        fi
    fi
    
    print_success "配置文件检查完成"
}

# 启动服务
start_services() {
    print_info "启动代理池负载均衡系统..."
    
    # 停止可能存在的旧容器
    docker-compose down 2>/dev/null || true
    
    # 构建并启动服务
    docker-compose up -d --build
    
    print_success "服务启动完成"
}

# 检查服务状态
check_services() {
    print_info "检查服务状态..."
    
    sleep 5  # 等待服务启动
    
    # 检查Redis
    if docker-compose exec redis redis-cli ping | grep -q "PONG"; then
        print_success "Redis 服务正常"
    else
        print_error "Redis 服务异常"
    fi
    
    # 检查YQProxy API
    if curl -s http://localhost:5555/health > /dev/null; then
        print_success "YQProxy API 服务正常"
    else
        print_warning "YQProxy API 服务可能还在启动中..."
    fi
    
    # 检查GoProxy负载均衡器
    if curl -s http://localhost:7890 > /dev/null 2>&1; then
        print_success "GoProxy 负载均衡器正常"
    else
        print_warning "GoProxy 负载均衡器可能还在启动中..."
    fi
}

# 显示服务信息
show_info() {
    print_info "服务信息:"
    echo "  Redis:              localhost:6379"
    echo "  YQProxy API:        http://localhost:5555"
    echo "  代理入口:           http://localhost:7890"
    echo ""
    print_info "使用方法:"
    echo "  设置HTTP代理:       localhost:7890"
    echo "  查看API文档:        http://localhost:5555"
    echo "  查看服务状态:       docker-compose ps"
    echo "  查看日志:           docker-compose logs -f"
    echo "  停止服务:           docker-compose down"
}

# 主函数
main() {
    echo "========================================"
    echo "    代理池负载均衡系统启动脚本"
    echo "========================================"
    echo ""
    
    case "${1:-start}" in
        "start")
            check_dependencies
            check_config
            start_services
            check_services
            show_info
            ;;
        "stop")
            print_info "停止服务..."
            docker-compose down
            print_success "服务已停止"
            ;;
        "restart")
            print_info "重启服务..."
            docker-compose down
            docker-compose up -d --build
            print_success "服务已重启"
            ;;
        "status")
            docker-compose ps
            ;;
        "logs")
            docker-compose logs -f
            ;;
        *)
            echo "使用方法: $0 {start|stop|restart|status|logs}"
            echo ""
            echo "  start   - 启动所有服务（默认）"
            echo "  stop    - 停止所有服务"
            echo "  restart - 重启所有服务"
            echo "  status  - 查看服务状态"
            echo "  logs    - 查看服务日志"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
