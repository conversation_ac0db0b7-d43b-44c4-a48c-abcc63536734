"""
代理测试服务
重构自原有测试逻辑，只依赖models层，提供纯净的代理测试功能
"""

import asyncio
import random
from typing import Dict, List, Optional
from dataclasses import dataclass

import aiohttp
from aiohttp_socks import ProxyConnector
from yqproxy.utils import get_logger

# 只依赖models层
from yqproxy.models import Proxy, Protocol

# 使用统一的配置管理
from yqproxy.config import TestConfig, get_test_config
from yqproxy.config.constants import (
    INFO_MSG_BATCH_TEST_START,
    INFO_MSG_BATCH_TEST_COMPLETE,
    WARNING_MSG_UNSUPPORTED_PROTOCOL
)


@dataclass 
class TestResult:
    """测试结果"""
    proxy: Proxy
    success: bool
    error: Optional[str] = None
    response_time: Optional[float] = None
    status_code: Optional[int] = None


@dataclass
class TestStats:
    """测试统计"""
    total_tested: int = 0
    total_success: int = 0
    http_tested: int = 0
    http_success: int = 0
    socks4_tested: int = 0
    socks4_success: int = 0
    socks5_tested: int = 0
    socks5_success: int = 0
    
    def get_success_rate(self, protocol: str = None) -> float:
        """获取成功率"""
        if protocol:
            tested_key = f"{protocol}_tested"
            success_key = f"{protocol}_success"
            tested = getattr(self, tested_key, 0)
            success = getattr(self, success_key, 0)
        else:
            tested = self.total_tested
            success = self.total_success
        
        return round(success / tested * 100, 2) if tested > 0 else 0.0
    
    def to_dict(self) -> dict:
        """转换为字典"""
        result = {
            'total_tested': self.total_tested,
            'total_success': self.total_success,
            'total_success_rate': self.get_success_rate(),
        }
        
        for protocol in ['http', 'socks4', 'socks5']:
            tested_key = f'{protocol}_tested'
            success_key = f'{protocol}_success'
            rate_key = f'{protocol}_success_rate'
            
            result[tested_key] = getattr(self, tested_key, 0)
            result[success_key] = getattr(self, success_key, 0)
            result[rate_key] = self.get_success_rate(protocol)
        
        return result


class ProxyTester:
    """
    代理测试服务
    提供协议感知的代理测试功能
    """
    
    # 内置股票池 - 选择大盘股和指数
    STOCK_POOL = [
        '1.000001',  # 上证指数
        '1.000016',  # 上证50
        '1.000300',  # 沪深300
        '0.399001',  # 深证成指
        '0.399006',  # 创业板指
        '1.600519',  # 贵州茅台
        '0.000858',  # 五粮液
        '1.600036',  # 招商银行
        '0.000002',  # 万科A
        '0.000001',  # 平安银行
    ]
    
    # 内置日期池 - 最近的稳定交易日
    DATE_POOL = [
        '20250701',  # 2025年7月1日
        '20250515',  # 2025年5月15日
        '20241230',  # 2024年12月30日
        '20241227',  # 2024年12月27日
        '20241226',  # 2024年12月26日
        '20241225',  # 2024年12月25日
        '20241224',  # 2024年12月24日
        '20241223',  # 2024年12月23日
        '20241220',  # 2024年12月20日
        '20241219',  # 2024年12月19日
    ]
    
    # 时间周期
    PERIOD_POOL = [
        '101',  # 日K线
        '102',  # 周K线
        '103',  # 月K线
    ]
    
    def __init__(self, config: TestConfig = None):
        """
        初始化代理测试器
        
        Args:
            config: 测试配置，None时使用全局默认配置
        """
        self.config = config or get_test_config()
        self.stats = TestStats()
        self.logger = get_logger(__name__)
    
    def _get_eastmoney_url(self) -> str:
        """
        生成东方财富的随机测试URL
        
        Returns:
            随机的东方财富测试URL
        """
        stock = random.choice(self.STOCK_POOL)
        date = random.choice(self.DATE_POOL)
        period = random.choice(self.PERIOD_POOL)
        
        url = (f'https://push2his.eastmoney.com/api/qt/stock/kline/get?'
               f'fields1=f1,f2,f3,f4,f5,f6&'
               f'fields2=f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61,f116&'
               f'ut=7eea3edcaed734bea9cbfc24409ed989&'
               f'klt={period}&fqt=0&secid={stock}&beg={date}&end={date}')
        
        return url
    
    def _get_test_url(self) -> str:
        """
        根据测试模式获取测试URL
        
        Returns:
            测试URL
        """
        if self.config.test_url_mode == 'eastmoney':
            return self._get_eastmoney_url()
        else:
            return self.config.test_url
    
    def _update_stats(self, protocol: str, success: bool = False):
        """
        更新统计信息
        
        Args:
            protocol: 协议类型
            success: 是否测试成功
        """
        self.stats.total_tested += 1
        if success:
            self.stats.total_success += 1
        
        # 协议特定统计
        if protocol in ['http', 'https']:
            self.stats.http_tested += 1
            if success:
                self.stats.http_success += 1
        elif protocol == 'socks4':
            self.stats.socks4_tested += 1
            if success:
                self.stats.socks4_success += 1
        elif protocol == 'socks5':
            self.stats.socks5_tested += 1
            if success:
                self.stats.socks5_success += 1
    
    async def _test_http_proxy(self, proxy: Proxy) -> TestResult:
        """
        测试HTTP/HTTPS代理
        
        Args:
            proxy: 代理对象
            
        Returns:
            测试结果
        """
        import time
        start_time = time.time()
        test_url = self._get_test_url()
        
        try:
            # 构建代理URL
            proxy_url = proxy.url
            
            # 创建连接器，禁用SSL验证以提高性能
            connector = aiohttp.TCPConnector(ssl=False, limit=100, limit_per_host=30)
            
            async with aiohttp.ClientSession(connector=connector) as session:
                async with session.get(
                    test_url,
                    proxy=proxy_url,
                    timeout=aiohttp.ClientTimeout(total=self.config.timeout),
                    allow_redirects=False
                ) as response:
                    response_time = time.time() - start_time
                    success = response.status in self.config.valid_status_codes
                    
                    self._update_stats(proxy.protocol, success)
                    
                    # 移除DEBUG日志，保持输出简洁
                    # self.logger.debug(f'HTTP代理测试: {proxy.address} -> 状态码 {response.status}, 成功: {success}')
                    
                    return TestResult(
                        proxy=proxy,
                        success=success,
                        response_time=response_time,
                        status_code=response.status
                    )
                    
        except Exception as e:
            response_time = time.time() - start_time
            error_msg = f"{type(e).__name__}: {str(e)[:100]}"
            
            self._update_stats(proxy.protocol, False)
            
            # 移除DEBUG日志，保持输出简洁
            # self.logger.debug(f'HTTP代理测试失败: {proxy.address} -> {error_msg}')
            
            return TestResult(
                proxy=proxy,
                success=False,
                error=error_msg,
                response_time=response_time
            )
    
    async def _test_socks_proxy(self, proxy: Proxy) -> TestResult:
        """
        测试SOCKS代理
        
        Args:
            proxy: 代理对象
            
        Returns:
            测试结果
        """
        import time
        start_time = time.time()
        test_url = self._get_test_url()
        
        try:
            # 构建SOCKS代理URL
            if proxy.protocol == Protocol.SOCKS5 and proxy.username and proxy.password:
                # SOCKS5支持认证
                proxy_url = f"socks5://{proxy.username}:{proxy.password}@{proxy.address}"
            else:
                proxy_url = f"{proxy.protocol}://{proxy.address}"
            
            # 创建SOCKS连接器，禁用SSL验证
            connector = ProxyConnector.from_url(proxy_url, ssl=False)
            
            async with aiohttp.ClientSession(connector=connector) as session:
                async with session.get(
                    test_url,
                    timeout=aiohttp.ClientTimeout(total=self.config.timeout),
                    allow_redirects=False
                ) as response:
                    response_time = time.time() - start_time
                    success = response.status in self.config.valid_status_codes
                    
                    self._update_stats(proxy.protocol, success)
                    
                    # 移除DEBUG日志，保持输出简洁
                    # self.logger.debug(f'SOCKS代理测试: {proxy.address} -> 状态码 {response.status}, 成功: {success}')
                    
                    return TestResult(
                        proxy=proxy,
                        success=success,
                        response_time=response_time,
                        status_code=response.status
                    )
                    
        except Exception as e:
            response_time = time.time() - start_time
            error_msg = f"{type(e).__name__}: {str(e)[:100]}"
            
            self._update_stats(proxy.protocol, False)
            
            # 移除DEBUG日志，保持输出简洁
            # self.logger.debug(f'SOCKS代理测试失败: {proxy.address} -> {error_msg}')
            
            return TestResult(
                proxy=proxy,
                success=False,
                error=error_msg,
                response_time=response_time
            )
    
    async def test_single_proxy(self, proxy: Proxy) -> TestResult:
        """
        测试单个代理
        
        Args:
            proxy: 代理对象
            
        Returns:
            测试结果
        """
        if proxy.protocol in [Protocol.HTTP, Protocol.HTTPS]:
            return await self._test_http_proxy(proxy)
        elif proxy.protocol in [Protocol.SOCKS4, Protocol.SOCKS5]:
            return await self._test_socks_proxy(proxy)
        else:
            error_msg = WARNING_MSG_UNSUPPORTED_PROTOCOL.format(protocol=proxy.protocol)
            self.logger.warning(error_msg)
            return TestResult(
                proxy=proxy,
                success=False,
                error=error_msg
            )
    
    async def test_proxy_batch(self, proxies: List[Proxy]) -> List[TestResult]:
        """
        批量测试代理
        
        Args:
            proxies: 代理列表
            
        Returns:
            测试结果列表
        """
        if not proxies:
            return []
        
        self.logger.info(INFO_MSG_BATCH_TEST_START.format(count=len(proxies)))
        
        # 创建测试任务
        tasks = [self.test_single_proxy(proxy) for proxy in proxies]
        
        # 并发执行所有测试
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理结果
        test_results = []
        success_count = 0
        error_count = 0
        
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                # 异常情况
                error_count += 1
                test_results.append(TestResult(
                    proxy=proxies[i],
                    success=False,
                    error=f"{type(result).__name__}: {str(result)[:100]}"
                ))
                # 移除DEBUG日志，保持输出简洁
                # self.logger.debug(f'代理测试异常: {proxies[i].address} -> {type(result).__name__}: {str(result)[:100]}')
            else:
                # 正常结果
                test_results.append(result)
                if result.success:
                    success_count += 1
        
        self.logger.info(INFO_MSG_BATCH_TEST_COMPLETE.format(
            success=success_count, 
            failed=len(proxies) - success_count, 
            error=error_count
        ))
        
        return test_results
    
    def get_stats(self) -> Dict:
        """
        获取测试统计信息
        
        Returns:
            统计信息字典
        """
        return self.stats.to_dict()
    
    def reset_stats(self):
        """重置统计信息"""
        self.stats = TestStats()
    
    def get_config(self) -> Dict:
        """
        获取当前配置
        
        Returns:
            配置信息字典
        """
        return {
            'test_url_mode': self.config.test_url_mode,
            'test_url': self.config.test_url,
            'timeout': self.config.timeout,
            'valid_status_codes': self.config.valid_status_codes
        }


# 便利函数已移除，直接使用 ProxyTester 类