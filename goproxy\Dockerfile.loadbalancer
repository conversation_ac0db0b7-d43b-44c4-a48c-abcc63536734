# 多阶段构建
FROM golang:1.21-alpine AS builder

# 安装必要的包
RUN apk add --no-cache git ca-certificates tzdata

# 设置工作目录
WORKDIR /app

# 复制go mod文件
COPY go.mod go.sum ./

# 下载依赖
RUN go mod download

# 复制源代码
COPY . .

# 构建应用
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o proxy-lb ./cmd/loadbalancer

# 最终镜像
FROM alpine:latest

# 安装ca证书和时区数据
RUN apk --no-cache add ca-certificates tzdata

# 设置时区
ENV TZ=Asia/Shanghai

# 创建非root用户
RUN addgroup -g 1001 -S proxy && \
    adduser -u 1001 -S proxy -G proxy

# 设置工作目录
WORKDIR /app

# 从构建阶段复制二进制文件
COPY --from=builder /app/proxy-lb .

# 创建配置目录
RUN mkdir -p /app/config && \
    chown -R proxy:proxy /app

# 切换到非root用户
USER proxy

# 暴露端口
EXPOSE 7890

# 启动命令
CMD ["./proxy-lb"]
