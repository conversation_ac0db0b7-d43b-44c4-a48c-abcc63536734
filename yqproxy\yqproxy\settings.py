"""
YQProxy 配置文件
按照逻辑分组组织，提供清晰的配置结构
"""

import platform
from os.path import dirname, abspath, join
from environs import Env

# ============================================================================
# 初始化配置
# ============================================================================
env = Env()
ROOT_DIR = dirname(dirname(abspath(__file__)))
env.read_env(join(ROOT_DIR, '.env'))

# ============================================================================
# 系统常量定义
# ============================================================================
# 自动检测当前操作系统
IS_WINDOWS = platform.system().lower() == 'windows'

# WSGI容器常量
APP_PROD_METHOD_GEVENT = 'gevent'
APP_PROD_METHOD_TORNADO = 'tornado'
APP_PROD_METHOD_MEINHELD = 'meinheld'

# ============================================================================
# 🚀 应用环境配置（最优先）
# ============================================================================
# 调试模式（统一控制调试相关功能）
# 影响：日志级别、API调试模式、自动重载、WSGI容器选择
DEBUG_MODE = env.bool('DEBUG_MODE', False)  # 生产环境默认False

# 生产环境WSGI容器选择
APP_PROD_METHOD = env.str('APP_PROD_METHOD', APP_PROD_METHOD_GEVENT).lower()

# ============================================================================
# ⚙️ 核心服务启用开关
# ============================================================================
ENABLE_GETTER = env.bool('ENABLE_GETTER', True)  # 代理获取器
ENABLE_TESTER = env.bool('ENABLE_TESTER', True)  # 代理测试器
ENABLE_SERVER = env.bool('ENABLE_SERVER', True)  # API服务器

# ============================================================================
# 🔧 Redis基础配置
# ============================================================================
# 支持两种配置前缀：PROXYPOOL_REDIS_* 或 REDIS_*
REDIS_HOST = env.str('PROXYPOOL_REDIS_HOST', 
                     env.str('REDIS_HOST', '127.0.0.1'))
REDIS_PORT = env.int('PROXYPOOL_REDIS_PORT', 
                     env.int('REDIS_PORT', 6379))
REDIS_PASSWORD = env.str('PROXYPOOL_REDIS_PASSWORD',
                         env.str('REDIS_PASSWORD', None))
REDIS_DB = env.int('PROXYPOOL_REDIS_DB', 
                   env.int('REDIS_DB', 0))

# Redis连接字符串（优先级最高）
REDIS_CONNECTION_STRING = env.str('PROXYPOOL_REDIS_CONNECTION_STRING', 
                                  env.str('REDIS_CONNECTION_STRING', None))

# ============================================================================
# 🔄 Redis性能优化配置
# ============================================================================
REDIS_PIPELINE_ENABLED = env.bool('REDIS_PIPELINE_ENABLED', True)
REDIS_PIPELINE_BATCH_SIZE = env.int('REDIS_PIPELINE_BATCH_SIZE', 100)
REDIS_PIPELINE_MAX_SIZE = env.int('REDIS_PIPELINE_MAX_SIZE', 500)
REDIS_PIPELINE_USE_TRANSACTION = env.bool('REDIS_PIPELINE_USE_TRANSACTION', True)

# ============================================================================
# 📊 代理评分配置
# ============================================================================
# 从统一配置导入，保持一致性
from yqproxy.config.constants import (
    PROXY_SCORE_MAX,      # 代理最高分数（100）
    PROXY_SCORE_MIN,      # 代理最低分数（0）
    PROXY_SCORE_INIT,     # 代理初始分数（10）
    PROXY_SCORE_DECREASE  # 测试失败减分（1）
)

# ============================================================================
# 📦 代理池容量管理
# ============================================================================
PROXY_NUMBER_MAX_INVALID = env.int('PROXY_NUMBER_MAX_INVALID', 1000)  # 总容量上限
PROXY_NUMBER_MIN_VALID = env.int('PROXY_NUMBER_MIN_VALID', 500)       # 最少有效数量

# ============================================================================
# ⏰ 调度器配置
# ============================================================================
SCHEDULE_GETTER_INTERVAL = env.int('SCHEDULE_GETTER_INTERVAL', 300)  # 获取器间隔(秒)
SCHEDULE_TESTER_INTERVAL = env.int('SCHEDULE_TESTER_INTERVAL', 300)  # 测试器间隔(秒)

# ============================================================================
# 🌐 代理获取器配置
# ============================================================================
GETTER_MAX_WORKERS = env.int('GETTER_MAX_WORKERS', 10)  # 并发工作线程数

# ============================================================================
# 🧪 代理测试器配置
# ============================================================================
# 测试模式配置
TEST_URL_MODE = env.str('TEST_URL_MODE', 'common')  # common或eastmoney
TEST_URL = env.str('TEST_URL', 'https://httpbin.org/ip')
TEST_TIMEOUT = env.int('TEST_TIMEOUT', 10)

# 批处理和并发配置
TESTER_BATCH_SIZE = env.int('TESTER_BATCH_SIZE', 50)  # 每批测试的代理数量
TESTER_MAX_CONCURRENT = env.int('TESTER_MAX_CONCURRENT', 20)  # 批内最大并发连接数

# 验证配置
TEST_VALID_STATUS = env.list('TEST_VALID_STATUS', [200, 302])
TEST_DONT_SET_MAX_SCORE = env.bool('TEST_DONT_SET_MAX_SCORE', False)

# ============================================================================
# 🌐 API服务配置
# ============================================================================
API_HOST = env.str('API_HOST', '0.0.0.0')
API_PORT = env.int('API_PORT', 5555)
API_KEY = env.str('API_KEY', '')  # 空字符串表示不启用认证
API_TIMEOUT = env.int('API_TIMEOUT', 5)
API_THREADED = env.bool('API_THREADED', True)
API_BATCH_LIMIT = env.int('API_BATCH_LIMIT', 100)  # 单次批量获取代理的最大数量限制

# ============================================================================
# 📝 日志配置
# ============================================================================
LOG_ENABLED = env.bool('LOG_ENABLED', True)
LOG_DIR = join(ROOT_DIR, env.str('LOG_DIR', 'logs'))
LOG_PATH = env.str('LOG_PATH', join(LOG_DIR, 'yqproxy.log'))

# 日志级别根据DEBUG_MODE自动调整
LOG_LEVEL = env.str('LOG_LEVEL', 'DEBUG' if DEBUG_MODE else 'INFO')

# 日志轮转和保留
LOG_ROTATION = env.str('LOG_ROTATION', '00:00')  # 每天午夜轮转
LOG_RETENTION = env.str('LOG_RETENTION', '30 days')  # 保留30天

# 控制台输出（调试模式默认开启）
LOG_CONSOLE = env.bool('LOG_CONSOLE', DEBUG_MODE)
