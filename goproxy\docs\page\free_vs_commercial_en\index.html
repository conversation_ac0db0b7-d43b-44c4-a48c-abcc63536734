<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
<meta name="pinterest" content="nopin">
<meta name="viewport" content="width=device-width,minimum-scale=1,initial-scale=1">
<meta name="generator" content="Hugo 0.56.3" />



<link rel="canonical" href="https://snail007.goproxyauth.com/goproxy/page/free_vs_commercial_en/">


    <link href="//maxcdn.bootstrapcdn.com/font-awesome/4.5.0/css/font-awesome.min.css" rel="stylesheet">
    <link rel="stylesheet" href="//maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css">
    <link rel="stylesheet" href="//cdnjs.cloudflare.com/ajax/libs/highlight.js/8.4/styles/solarized_dark.min.css">
    <title>GOPROXY Free Version VS Commercial Version - GOPROXY IN ACTION</title>
    
<meta name="description" content="GOPROXY Free Version VS Commercial Version">

<meta property="og:title" content="GOPROXY Free Version VS Commercial Version - GOPROXY IN ACTION">
<meta property="og:type" content="article">
<meta property="og:url" content="https://snail007.goproxyauth.com/goproxy/page/free_vs_commercial_en/">
<meta property="og:image" content="https://snail007.goproxyauth.com/goproxy/images/default.png">
<meta property="og:site_name" content="GOPROXY IN ACTION">
<meta property="og:description" content="GOPROXY Free Version VS Commercial Version">
<meta property="og:locale" content="zh_CN">

<meta name="twitter:card" content="summary_large_image">
<meta name="twitter:site" content="GOPROXY IN ACTION">
<meta name="twitter:url" content="https://snail007.goproxyauth.com/goproxy/page/free_vs_commercial_en/">
<meta name="twitter:title" content="GOPROXY Free Version VS Commercial Version - GOPROXY IN ACTION">
<meta name="twitter:description" content="GOPROXY Free Version VS Commercial Version">
<meta name="twitter:image" content="https://snail007.goproxyauth.com/goproxy/images/default.png">


<script type="application/ld+json">
  {
    "@context": "http://schema.org",
    "@type": "NewsArticle",
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id":"https:\/\/snail007.goproxyauth.com\/goproxy\/"
    },
    "headline": "GOPROXY Free Version VS Commercial Version - GOPROXY IN ACTION",
    "image": {
      "@type": "ImageObject",
      "url": "https:\/\/snail007.goproxyauth.com\/goproxy\/images\/default.png",
      "height": 800,
      "width": 800
    },
    "datePublished": "2019-06-26T13:19:15JST",
    "dateModified": "2019-06-26T13:19:15JST",
    "author": {
      "@type": "Person",
      "name": "GOPROXY IN ACTION"
    },
    "publisher": {
      "@type": "Organization",
      "name": "GOPROXY IN ACTION",
      "logo": {
        "@type": "ImageObject",
        "url": "https:\/\/snail007.goproxyauth.com\/goproxy\/images/logo.png",
        "width": 600,
        "height": 60
      }
    },
    "description": "GOPROXY Free Version VS Commercial Version"
  }
</script>


    <link href="https://snail007.goproxyauth.com/goproxy/css/styles.css" rel="stylesheet">
    

  </head>

  <body>
    
    
    

    <header class="l-header">
      <nav class="navbar navbar-default">
        <div class="container">
          <div class="navbar-header">
            <button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#navbar" aria-expanded="false">
              <span class="sr-only">Toggle navigation</span>
              <span class="icon-bar"></span>
              <span class="icon-bar"></span>
              <span class="icon-bar"></span>
            </button>
            <a class="navbar-brand" href="https://snail007.goproxyauth.com/goproxy/">GOPROXY IN ACTION</a>
          </div>

          
          <div id="navbar" class="collapse navbar-collapse">
            
            <ul class="nav navbar-nav navbar-right">
              
              
              <li><a href="https://github.com/snail007/goproxy/releases/">下载</a></li>
              
              
              
              <li><a href="https://snail007.goproxyauth.com/goproxy/categories/goproxy%E6%89%8B%E5%86%8C/">实战</a></li>
              
              
              
              <li><a href="https://snail007.goproxyauth.com/goproxy/manual/zh/#/">手册</a></li>
              
              
              
              <li><a href="/goproxy/about/">关于</a></li>
              
              
            </ul>
            
          </div>
          

        </div>
      </nav>
    </header>

    <main>
      <div class="container">
        
<div class="row">
  <div class="col-md-8">

    <nav class="p-crumb">
      <ol class="breadcrumb">
        <li><a href="https://snail007.goproxyauth.com/goproxy/"><i class="fa fa-home" aria-hidden="true"></i></a></li>
        
        <li itemscope="" itemtype="http://data-vocabulary.org/Breadcrumb"><a href="https://snail007.goproxyauth.com/goproxy/page/" itemprop="url"><span itemprop="title">page</span></a></li>
        
        <li class="active">GOPROXY Free Version VS Commercial Version</li>
      </ol>
    </nav>

    <article class="single">
  <header>
    <ul class="p-facts">
      <li><i class="fa fa-calendar" aria-hidden="true"></i><time datetime="2019-06-26T13:19:15JST">Jun 26, 2019</time></li>
      <li><i class="fa fa-bookmark" aria-hidden="true"></i><a href="https://snail007.goproxyauth.com/goproxy/page/">page</a></li>
      
    </ul>

    <h1 class="title">GOPROXY Free Version VS Commercial Version</h1>
  </header>

  

  <div class="article-body">

<h4 id="currently-goproxy-and-sdk-are-available-in-both-free-and-commercial-versions">Currently, GOPROXY and SDK are available in both free and commercial versions.</h4>

<table>
<thead>
<tr>
<th align="left">Function</th>
<th align="center">Free</th>
<th align="center">|Commercial　　　</th>
</tr>
</thead>

<tbody>
<tr>
<td align="left"><code>TCP forwarding</code></td>
<td align="center">√</td>
<td align="center">√</td>
</tr>

<tr>
<td align="left"><code>UDP forwarding</code></td>
<td align="center">√</td>
<td align="center">√</td>
</tr>

<tr>
<td align="left"><code>HTTP(S)Agent</code></td>
<td align="center">√</td>
<td align="center">√</td>
</tr>

<tr>
<td align="left"><code>Socks5 Agent</code></td>
<td align="center">√</td>
<td align="center">√</td>
</tr>

<tr>
<td align="left"><code>Socks5 proxy UDP support</code></td>
<td align="center">√</td>
<td align="center">√</td>
</tr>

<tr>
<td align="left"><code>SPS Integrated Agent</code></td>
<td align="center">√</td>
<td align="center">√</td>
</tr>

<tr>
<td align="left"><code>SPS superior certification</code></td>
<td align="center">√</td>
<td align="center">√</td>
</tr>

<tr>
<td align="left"><code>Intranet penetration</code></td>
<td align="center">√</td>
<td align="center">√</td>
</tr>

<tr>
<td align="left"><code>DNS proxy, anti-pollution, independent DNS service</code></td>
<td align="center">√</td>
<td align="center">√</td>
</tr>

<tr>
<td align="left"><code>HTTP(S) proxy authentication, authentication API, load balancing</code></td>
<td align="center">×</td>
<td align="center">√</td>
</tr>

<tr>
<td align="left"><code>SOCKS5 proxy authentication, authentication API, load balancing</code></td>
<td align="center">×</td>
<td align="center">√</td>
</tr>

<tr>
<td align="left"><code>SPS proxy authentication, authentication API, load balancing</code></td>
<td align="center">×</td>
<td align="center">√</td>
</tr>

<tr>
<td align="left"><code>STOP&amp;ONLY blacklist and whitelist</code></td>
<td align="center">×</td>
<td align="center">√</td>
</tr>

<tr>
<td align="left"><code>Client IP blacklist and whitelist</code></td>
<td align="center">×</td>
<td align="center">√</td>
</tr>

<tr>
<td align="left"><code>Check for updates, failed to exit</code></td>
<td align="center">×</td>
<td align="center">×</td>
</tr>

<tr>
<td align="left"><code>Single machine process limit</code></td>
<td align="center">×</td>
<td align="center">×</td>
</tr>

<tr>
<td align="left"><code>Manual full function</code></td>
<td align="center">×</td>
<td align="center">√</td>
</tr>

<tr>
<td align="left"><code>Need network authentication</code></td>
<td align="center">×</td>
<td align="center">√</td>
</tr>

<tr>
<td align="left"><code>Speed ​​limit function</code></td>
<td align="center">×</td>
<td align="center">√</td>
</tr>

<tr>
<td align="left"><code>http/socks5/tcp fllow IP</code></td>
<td align="center">×</td>
<td align="center">√</td>
</tr>

<tr>
<td align="left"><code>Target connection redirection</code></td>
<td align="center">×</td>
<td align="center">√</td>
</tr>

<tr>
<td align="left"><code>docker</code></td>
<td align="center">√</td>
<td align="center">√</td>
</tr>

<tr>
<td align="left"><code>Free update to more features of the commercial version</code></td>
<td align="center">×</td>
<td align="center">√</td>
</tr>

<tr>
<td align="left"><code>http(s)\socks\sps, superior ws/wss support path and independent encryption</code></td>
<td align="center">×</td>
<td align="center">√</td>
</tr>

<tr>
<td align="left"><code>Limit user or ip  traffic rate</code></td>
<td align="center">×</td>
<td align="center">√</td>
</tr>

<tr>
<td align="left"><code>Limit user or ip  connections count</code></td>
<td align="center">×</td>
<td align="center">√</td>
</tr>

<tr>
<td align="left"><code>API dynamic upstream</code></td>
<td align="center">×</td>
<td align="center">√</td>
</tr>

<tr>
<td align="left"><code>socks5/http(s)/sps/tcp/udp listen on range ports support</code></td>
<td align="center">×</td>
<td align="center">√</td>
</tr>
</tbody>
</table>

<h4 id="there-are-two-types-of-commercial-licenses">There are two types of commercial licenses</h4>

<ol>
<li><p>Program and machine binding, single machine mode authorization, charge for <code>machine code</code>.</p></li>

<li><p>The program is not bound to the machine, authorized code mode authorization, and charged for the <code>authorization code</code>.</p></li>
</ol>

<h4 id="commercial-version-uses">Commercial version uses:</h4>

<ol>
<li><p>Go to the authorization platform <a href="https://gpm.goproxyauth.com/?lang=en-US">https://gpm.goproxyauth.com/?lang=en-US</a> to register a user.</p></li>

<li><p>If you use the <code>machine code</code> authorization method, the first execution: <code>proxy http</code> , then waiting for it exit, and
then it will generate an id.txt file in the current directory, which is the machine code of the current machine. If
it is sdk, the MacCode method can be called to get the current machine. Machine code; then
purchase <code>card secret code</code>, go to the authorization platform background click menu to add machine code,
input <code>card secret code</code> to complete the activation of the machine code, then click on the <code>machine list</code> in the
menu, click on the list operation column <code>modify</code>, Fill in the machine code in id.txt into the machine code input
box, and then save to complete the authorization.</p></li>

<li><p>If it is authorized by the <code>Authorization Code</code> method, first purchase the authorization code, and then you will see your authorization code in the authorization platform. Add the authorization code parameter <code>--authcode your_authorization_code</code> to start the program. You can also set the environment variable <code>LIC_AUTHCODE</code> as the authorization code. If the parameter <code>--authcode</code> is not used, the program tries to obtain the authorization from the environment variable <code>LIC_AUTHCODE</code>. code. If it is sdk, the start method has an authorization code parameter, and the purchased authorization code can be used.</p></li>
</ol>

<h4 id="proxyadmin-authorization-code">proxyadmin <code>Authorization Code</code></h4>

<p><code>proxyadmin_coml</code> (commercial version) and <code>proxyadmin_vip</code> (VIP version) also support <code>authorization code</code> authorization, but the use of authorization code is different from goproxy.</p>

<p>After <code>proxyadmin_coml</code> or <code>proxyadmin_vip</code> installed, the system type is different, an empty file of authcode.txt will be generated in the following path, just input the purchased <code>authorization code</code> to file authcode.txt, and then Execute <code>proxy-admin restart</code> to restart the program.</p>

<ul>
<li>Linux：<code>/etc/gpa/authcode.txt</code></li>
<li>macOS：<code>/etc/gpa/authcode.txt</code></li>
<li>Windows：<code>C:\gpa\authcode.txt</code></li>
</ul>

<h4 id="remind">Remind:</h4>

<p>The commercial version will only check the authorization once online at the time of startup. It will not check the network again later. It will only be checked once the authorization code expires. If the authorization program cannot be successfully checked within 48 hours, it will exit.
If the system finds that the user maliciously propagates its own &lsquo;authorization code&rsquo;, the official has the right to terminate the use of the authorization code and is not responsible for it.</p>

<h4 id="payment">Payment</h4>

<p>Buy the commercial version, please email to : <EMAIL> or telegram : arraykeys .</p>

<p>USDT-TRC20 is accepted.</p>

<p>USDT-TRC20: <code>THsgQrG6M31YDY4fWkNTv1LbhiAqzfvxFA</code></p>

<h4 id="commercial-edition-trial">Commercial Edition Trial</h4>

<ol>
<li><p>Before purchasing the commercial version, you can try it for free for 7 days to determine if the GOPROXY Commercial
Edition feature fit your needs.</p></li>

<li><p>Trial steps: Log in to <a href="https://gpm.goproxyauth.com/?lang=en-US">Authorization Platform</a> -&gt; My Machine Code -&gt; Buy
Online -&gt; Click &ldquo;Start Trial&rdquo;.</p></li>
</ol>

<h4 id="commercial-edition-list">Commercial Edition List</h4>

<p>Software list<br />
1.<code>proxyadmin commercial</code> edition, price: 40$/machine/year, web console and full functions as same as goproxy commercial.<br />
2.<code>proxyadmin vip</code> edition, price: 20$/machine/year, web console and full functions as same as goproxy free edition.<br />
3.<code>gorproxy commercial</code> edition, price: 20$/machine/year, command line console and full functions of goproxy.<br />
4.<code>proxyadmin cluster</code> edition, price: 60$/machine/year, web console to manage  many proxies on huge machines that run gorproxy commercial edition.</p>

<h4 id="notice">Notice</h4>

<p>All features of vip edition as same as free edition, but vip edition service count unlimited.<br />
Of all products have no additional usage support, yiu have to read the manual patiently.</p>

<h4 id="can-t-access-the-authorized-solution">Can&rsquo;t access the authorized solution</h4>

<p>The commercial version must be online authorized. If you are using the proxy, the server can access the Internet, but the proxy can not access to the authorization information. Can be solved as follows.</p>

<h5 id="1-if-your-proxy-machine-can-access-the-internet-you-can-specify-the-dns-startup-proxy-because-the-machine-dns-may-not-correctly-resolve-the-updated-domain-name">1. If your proxy machine can access the Internet, you can specify the DNS startup proxy because the machine DNS may not correctly resolve the updated domain name.</h5>

<p>Linux startup command example:</p>

<pre><code class="language-shell">LIC_DNS=*******:53 ./proxy http -p :9090
</code></pre>

<p>Windows startup command example</p>

<pre><code class="language-shell">Set LIC_DNS=*******:53
./proxy.exe http -p :9090
</code></pre>

<h5 id="2-if-the-proxy-is-not-able-to-access-the-internet-directly-in-the-internal-network-environment-you-can-start-an-http-proxy-on-the-machine-that-can-access-the-internet-and-then-use-it-for-the-proxy">2. If the proxy is not able to access the Internet directly in the internal network environment, you can start an http proxy on the machine that can access the Internet, and then use it for the proxy.</h5>

<p>For example: The proxy is at <code>192.168.1.10</code> and cannot access the Internet directly.</p>

<p>The machine can access the Internet at <code>192.168.1.200</code>, then it can be started on the <code>192.168.1.200</code> machine:</p>

<p><code>./proxy http -p :8080 -q *******:53</code></p>

<p>Start the command above <code>192.168.1.10</code>:</p>

<p><code>LIC_PROXY=http://192.168.1.200:8080 ./proxy http -p :9090</code></p>

<h5 id="3-if-the-intranet-has-a-ready-made-http-proxy-then-you-don-t-need-to-start-a-proxy-yourself-in-the-2-steps-lic-proxy-can-use-the-ready-made-http-proxy-directly">3. If the intranet has a ready-made http proxy, then you don&rsquo;t need to start a proxy yourself in the 2 steps. LIC_PROXY can use the ready-made http proxy directly.</h5>
</div>

  <footer class="article-footer">
    
    
    
    <section class="bordered">
      <header>
        <div class="panel-title">CATEGORIES</div>
      </header>
      <div>
        <ul class="p-terms">
          
          <li><a href="https://snail007.goproxyauth.com/goproxy/categories/%E9%BB%98%E8%AE%A4%E5%88%86%E7%B1%BB/">默认分类</a></li>
          
        </ul>
      </div>
    </section>
    
    
    
    <section class="bordered">
      <header>
        <div class="panel-title">TAGS</div>
      </header>
      <div>
        <ul class="p-terms">
          
          <li><a href="https://snail007.goproxyauth.com/goproxy/tags/%E5%95%86%E4%B8%9A%E7%89%88/">商业版</a></li>
          
          <li><a href="https://snail007.goproxyauth.com/goproxy/tags/commercial/">commercial</a></li>
          
        </ul>
      </div>
    </section>
    
    
  </footer>

</article>


    <div id="disqus_thread"></div>
<script type="application/javascript">
    var disqus_config = function () {
    
    
    
    };
    (function() {
        if (["localhost", "127.0.0.1"].indexOf(window.location.hostname) != -1) {
            document.getElementById('disqus_thread').innerHTML = 'Disqus comments not available by default when the website is previewed locally.';
            return;
        }
        var d = document, s = d.createElement('script'); s.async = true;
        s.src = '//' + "goproxy" + '.disqus.com/embed.js';
        s.setAttribute('data-timestamp', +new Date());
        (d.head || d.body).appendChild(s);
    })();
</script>
<noscript>Please enable JavaScript to view the <a href="https://disqus.com/?ref_noscript">comments powered by Disqus.</a></noscript>
<a href="https://disqus.com" class="dsq-brlink">comments powered by <span class="logo-disqus">Disqus</span></a>
  </div>

  <div class="col-md-4">
    
<aside class="l-sidebar">

  <section class="panel panel-default">
    <div class="panel-heading">
      <div class="panel-title">LATESTS</div>
    </div>
    <div class="list-group">
      
      <a href="https://snail007.goproxyauth.com/goproxy/usage/first/" class="list-group-item">手册前言（使用必读）</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/page/free_vs_commercial/" class="list-group-item">GOPROXY免费版VS商业版</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/page/faq/goproxy%E5%B8%B8%E8%A7%81%E9%97%AE%E9%A2%98%E8%A7%A3%E7%AD%94/" class="list-group-item">常见问题解答</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/posts/domain-cf/" class="list-group-item">域名解析 域名控制台&#43;极客&#43;cloudflare 的配置方法</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/posts/windows-global-proxy-using-dns/" class="list-group-item">在windows下实现基于dns的自动全局代理</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/usage/tcp/" class="list-group-item">TCP代理</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/posts/cloudflare/" class="list-group-item">Cloudflare使用简明教程</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/posts/http-nat-cdn/" class="list-group-item">内网穿透也能用CDN？？？！</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/page/free_vs_commercial_en/" class="list-group-item">GOPROXY Free Version VS Commercial Version</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/posts/http_cdn_ws/" class="list-group-item">代理之套用CDN</a>
      
    </div>
  </section>

  
  <section class="panel panel-default">
    <div class="panel-heading">
      <div class="panel-title">CATEGORY</div>
    </div>
    <div class="list-group">
      
      <a href="https://snail007.goproxyauth.com/goproxy/categories/%E9%BB%98%E8%AE%A4%E5%88%86%E7%B1%BB" class="list-group-item">默认分类</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/categories/goproxy%E6%89%8B%E5%86%8C" class="list-group-item">goproxy手册</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/categories/%E6%9E%B6%E6%9E%84%E8%A7%A3%E8%AF%B4" class="list-group-item">架构解说</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/categories/%E7%BB%86%E8%AF%B4%E5%B1%82%E7%BA%A7" class="list-group-item">细说层级</a>
      
    </div>
  </section>
  
  <section class="panel panel-default">
    <div class="panel-heading">
      <div class="panel-title">TAG</div>
    </div>
    <div class="list-group">
      
      <a href="https://snail007.goproxyauth.com/goproxy/tags/cdn" class="list-group-item">cdn</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/tags/ws" class="list-group-item">ws</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/tags/cloudflare" class="list-group-item">cloudflare</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/tags/%E5%95%86%E4%B8%9A%E7%89%88" class="list-group-item">商业版</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/tags/commercial" class="list-group-item">commercial</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/tags/domain" class="list-group-item">domain</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/tags/tcp" class="list-group-item">tcp</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/tags/%E5%85%A8%E5%B1%80%E4%BB%A3%E7%90%86" class="list-group-item">全局代理</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/tags/%E5%86%85%E7%BD%91%E7%A9%BF%E9%80%8F" class="list-group-item">内网穿透</a>
      
    </div>
  </section>
  

</aside>


  </div>
</div>

      </div>
    </main>

    <footer class="l-footer">
      <div class="container">
        <p><span class="h-logo">&copy; GOPROXY IN ACTION</span></p>
      </div>
    </footer>

    <script src="//code.jquery.com/jquery-3.1.1.min.js"></script>
    <script src="//maxcdn.bootstrapcdn.com/bootstrap/3.3.7/js/bootstrap.min.js"></script>
    <script src="//cdnjs.cloudflare.com/ajax/libs/highlight.js/8.4/highlight.min.js"></script>
    <script>hljs.initHighlightingOnLoad();</script>
    <script>
    $(function(){
      $("img[css]").each(function(){
        $(this).attr("style",$(this).attr("css"));
      });
    });
    </script>
  </body>
</html>

