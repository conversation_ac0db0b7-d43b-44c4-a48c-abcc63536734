<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
<meta name="pinterest" content="nopin">
<meta name="viewport" content="width=device-width,minimum-scale=1,initial-scale=1">
<meta name="generator" content="Hugo 0.56.3" />



<link rel="canonical" href="https://snail007.goproxyauth.com/goproxy/posts/windows-global-proxy-using-dns/">


    <link href="//maxcdn.bootstrapcdn.com/font-awesome/4.5.0/css/font-awesome.min.css" rel="stylesheet">
    <link rel="stylesheet" href="//maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css">
    <link rel="stylesheet" href="//cdnjs.cloudflare.com/ajax/libs/highlight.js/8.4/styles/solarized_dark.min.css">
    <title>在windows下实现基于dns的自动全局代理 - GOPROXY IN ACTION</title>
    
<meta name="description" content="在windows下实现基于dns的自动全局代理">

<meta property="og:title" content="在windows下实现基于dns的自动全局代理 - GOPROXY IN ACTION">
<meta property="og:type" content="article">
<meta property="og:url" content="https://snail007.goproxyauth.com/goproxy/posts/windows-global-proxy-using-dns/">
<meta property="og:image" content="https://snail007.goproxyauth.com/goproxy/images/default.png">
<meta property="og:site_name" content="GOPROXY IN ACTION">
<meta property="og:description" content="在windows下实现基于dns的自动全局代理">
<meta property="og:locale" content="zh_CN">

<meta name="twitter:card" content="summary_large_image">
<meta name="twitter:site" content="GOPROXY IN ACTION">
<meta name="twitter:url" content="https://snail007.goproxyauth.com/goproxy/posts/windows-global-proxy-using-dns/">
<meta name="twitter:title" content="在windows下实现基于dns的自动全局代理 - GOPROXY IN ACTION">
<meta name="twitter:description" content="在windows下实现基于dns的自动全局代理">
<meta name="twitter:image" content="https://snail007.goproxyauth.com/goproxy/images/default.png">


<script type="application/ld+json">
  {
    "@context": "http://schema.org",
    "@type": "NewsArticle",
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id":"https:\/\/snail007.goproxyauth.com\/goproxy\/"
    },
    "headline": "在windows下实现基于dns的自动全局代理 - GOPROXY IN ACTION",
    "image": {
      "@type": "ImageObject",
      "url": "https:\/\/snail007.goproxyauth.com\/goproxy\/images\/default.png",
      "height": 800,
      "width": 800
    },
    "datePublished": "2019-07-04T17:54:31JST",
    "dateModified": "2019-07-04T17:54:31JST",
    "author": {
      "@type": "Person",
      "name": "GOPROXY IN ACTION"
    },
    "publisher": {
      "@type": "Organization",
      "name": "GOPROXY IN ACTION",
      "logo": {
        "@type": "ImageObject",
        "url": "https:\/\/snail007.goproxyauth.com\/goproxy\/images/logo.png",
        "width": 600,
        "height": 60
      }
    },
    "description": "在windows下实现基于dns的自动全局代理"
  }
</script>


    <link href="https://snail007.goproxyauth.com/goproxy/css/styles.css" rel="stylesheet">
    

  </head>

  <body>
    
    
    

    <header class="l-header">
      <nav class="navbar navbar-default">
        <div class="container">
          <div class="navbar-header">
            <button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#navbar" aria-expanded="false">
              <span class="sr-only">Toggle navigation</span>
              <span class="icon-bar"></span>
              <span class="icon-bar"></span>
              <span class="icon-bar"></span>
            </button>
            <a class="navbar-brand" href="https://snail007.goproxyauth.com/goproxy/">GOPROXY IN ACTION</a>
          </div>

          
          <div id="navbar" class="collapse navbar-collapse">
            
            <ul class="nav navbar-nav navbar-right">
              
              
              <li><a href="https://github.com/snail007/goproxy/releases/">下载</a></li>
              
              
              
              <li><a href="https://snail007.goproxyauth.com/goproxy/categories/goproxy%E6%89%8B%E5%86%8C/">实战</a></li>
              
              
              
              <li><a href="https://snail007.goproxyauth.com/goproxy/manual/zh/#/">手册</a></li>
              
              
              
              <li><a href="/goproxy/about/">关于</a></li>
              
              
            </ul>
            
          </div>
          

        </div>
      </nav>
    </header>

    <main>
      <div class="container">
        
<div class="row">
  <div class="col-md-8">

    <nav class="p-crumb">
      <ol class="breadcrumb">
        <li><a href="https://snail007.goproxyauth.com/goproxy/"><i class="fa fa-home" aria-hidden="true"></i></a></li>
        
        <li itemscope="" itemtype="http://data-vocabulary.org/Breadcrumb"><a href="https://snail007.goproxyauth.com/goproxy/posts/" itemprop="url"><span itemprop="title">posts</span></a></li>
        
        <li class="active">在windows下实现基于dns的自动全局代理</li>
      </ol>
    </nav>

    <article class="single">
  <header>
    <ul class="p-facts">
      <li><i class="fa fa-calendar" aria-hidden="true"></i><time datetime="2019-07-04T17:54:31JST">Jul 4, 2019</time></li>
      <li><i class="fa fa-bookmark" aria-hidden="true"></i><a href="https://snail007.goproxyauth.com/goproxy/posts/">posts</a></li>
      
    </ul>

    <h1 class="title">在windows下实现基于dns的自动全局代理</h1>
  </header>

  

  <div class="article-body">

<h3 id="在windows下实现基于dns的自动全局代理">在windows下实现基于dns的自动全局代理。</h3>

<p>本方法需要监听本地80和443端口，所以如果你有程序占用了这两个端口，请关闭相应的程序。</p>

<p>本文提供了两种方式：1.使用CDN作为中间传输层 2.不使用CDN；根据自己情况二选一即可。</p>

<h4 id="1-正常配置篇">1.正常配置篇</h4>

<p>服务端为机器A</p>

<p>1、在上级服务端执行：</p>

<p><code>./proxy http -t ws -p 0.0.0.0:8443  —-daemon</code></p>

<p>3、在电脑上：</p>

<p>1）修改proxy目录下面的hosts文件，该文件和proxy.exe程序一起。</p>

<p>添加如下内容：</p>

<pre><code class="language-text">127.0.0.1  **.**  

</code></pre>

<p>2）修改本地网络连接的dns地址为127.0.0.1。</p>

<p>3）本机启动一个二级sps监听80和443。</p>

<p><code>proxy.exe sps -P httpws://IP_A:8443 -p :80,:443 -q *******:53</code></p>

<p>4）本地启动一个dns监听53 <code>.\proxy.exe dns -P http://127.0.0.1:80 --hosts .\hosts</code> 。</p>

<h4 id="2-配合cdn配置篇">2.配合CDN配置篇</h4>

<p>1、配置好cloudflare，CDN加速域名为：your.domain.com。</p>

<p>2、在上级服务端执行：</p>

<p>首先生成证书：</p>

<p><code>proxy keygen -C proxy</code></p>

<p>然后：</p>

<p><code>./proxy http -t wss -p 0.0.0.0:8443 —-daemon</code></p>

<p>3、在电脑上：</p>

<p>1）修改proxy目录下面的hosts文件，该文件和proxy.exe程序一起。</p>

<p>添加如下内容：</p>

<pre><code class="language-text">*************** your.domain.com

127.0.0.1  **.**  

</code></pre>

<p>2）修改本地网络连接的dns地址为127.0.0.1。</p>

<p>3）本机启动一个二级sps监听80和443。</p>

<p><code>proxy.exe sps -P httpwss://your.domain.com:8443 -p :80,:443 -q *******:53</code></p>

<p>4）本地启动一个dns监听53 <code>.\proxy.exe dns -P http://127.0.0.1:80 -—hosts .\hosts</code> 。</p>
</div>

  <footer class="article-footer">
    
    
    
    <section class="bordered">
      <header>
        <div class="panel-title">CATEGORIES</div>
      </header>
      <div>
        <ul class="p-terms">
          
          <li><a href="https://snail007.goproxyauth.com/goproxy/categories/%E9%BB%98%E8%AE%A4%E5%88%86%E7%B1%BB/">默认分类</a></li>
          
        </ul>
      </div>
    </section>
    
    
    
    <section class="bordered">
      <header>
        <div class="panel-title">TAGS</div>
      </header>
      <div>
        <ul class="p-terms">
          
          <li><a href="https://snail007.goproxyauth.com/goproxy/tags/%E5%85%A8%E5%B1%80%E4%BB%A3%E7%90%86/">全局代理</a></li>
          
        </ul>
      </div>
    </section>
    
    
  </footer>

</article>


    <div id="disqus_thread"></div>
<script type="application/javascript">
    var disqus_config = function () {
    
    
    
    };
    (function() {
        if (["localhost", "127.0.0.1"].indexOf(window.location.hostname) != -1) {
            document.getElementById('disqus_thread').innerHTML = 'Disqus comments not available by default when the website is previewed locally.';
            return;
        }
        var d = document, s = d.createElement('script'); s.async = true;
        s.src = '//' + "goproxy" + '.disqus.com/embed.js';
        s.setAttribute('data-timestamp', +new Date());
        (d.head || d.body).appendChild(s);
    })();
</script>
<noscript>Please enable JavaScript to view the <a href="https://disqus.com/?ref_noscript">comments powered by Disqus.</a></noscript>
<a href="https://disqus.com" class="dsq-brlink">comments powered by <span class="logo-disqus">Disqus</span></a>
  </div>

  <div class="col-md-4">
    
<aside class="l-sidebar">

  <section class="panel panel-default">
    <div class="panel-heading">
      <div class="panel-title">LATESTS</div>
    </div>
    <div class="list-group">
      
      <a href="https://snail007.goproxyauth.com/goproxy/usage/first/" class="list-group-item">手册前言（使用必读）</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/page/free_vs_commercial/" class="list-group-item">GOPROXY免费版VS商业版</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/page/faq/goproxy%E5%B8%B8%E8%A7%81%E9%97%AE%E9%A2%98%E8%A7%A3%E7%AD%94/" class="list-group-item">常见问题解答</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/posts/domain-cf/" class="list-group-item">域名解析 域名控制台&#43;极客&#43;cloudflare 的配置方法</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/posts/windows-global-proxy-using-dns/" class="list-group-item">在windows下实现基于dns的自动全局代理</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/usage/tcp/" class="list-group-item">TCP代理</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/posts/cloudflare/" class="list-group-item">Cloudflare使用简明教程</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/posts/http-nat-cdn/" class="list-group-item">内网穿透也能用CDN？？？！</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/page/free_vs_commercial_en/" class="list-group-item">GOPROXY Free Version VS Commercial Version</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/posts/http_cdn_ws/" class="list-group-item">代理之套用CDN</a>
      
    </div>
  </section>

  
  <section class="panel panel-default">
    <div class="panel-heading">
      <div class="panel-title">CATEGORY</div>
    </div>
    <div class="list-group">
      
      <a href="https://snail007.goproxyauth.com/goproxy/categories/%E9%BB%98%E8%AE%A4%E5%88%86%E7%B1%BB" class="list-group-item">默认分类</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/categories/goproxy%E6%89%8B%E5%86%8C" class="list-group-item">goproxy手册</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/categories/%E6%9E%B6%E6%9E%84%E8%A7%A3%E8%AF%B4" class="list-group-item">架构解说</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/categories/%E7%BB%86%E8%AF%B4%E5%B1%82%E7%BA%A7" class="list-group-item">细说层级</a>
      
    </div>
  </section>
  
  <section class="panel panel-default">
    <div class="panel-heading">
      <div class="panel-title">TAG</div>
    </div>
    <div class="list-group">
      
      <a href="https://snail007.goproxyauth.com/goproxy/tags/cdn" class="list-group-item">cdn</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/tags/ws" class="list-group-item">ws</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/tags/cloudflare" class="list-group-item">cloudflare</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/tags/%E5%95%86%E4%B8%9A%E7%89%88" class="list-group-item">商业版</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/tags/commercial" class="list-group-item">commercial</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/tags/domain" class="list-group-item">domain</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/tags/tcp" class="list-group-item">tcp</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/tags/%E5%85%A8%E5%B1%80%E4%BB%A3%E7%90%86" class="list-group-item">全局代理</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/tags/%E5%86%85%E7%BD%91%E7%A9%BF%E9%80%8F" class="list-group-item">内网穿透</a>
      
    </div>
  </section>
  

</aside>


  </div>
</div>

      </div>
    </main>

    <footer class="l-footer">
      <div class="container">
        <p><span class="h-logo">&copy; GOPROXY IN ACTION</span></p>
      </div>
    </footer>

    <script src="//code.jquery.com/jquery-3.1.1.min.js"></script>
    <script src="//maxcdn.bootstrapcdn.com/bootstrap/3.3.7/js/bootstrap.min.js"></script>
    <script src="//cdnjs.cloudflare.com/ajax/libs/highlight.js/8.4/highlight.min.js"></script>
    <script>hljs.initHighlightingOnLoad();</script>
    <script>
    $(function(){
      $("img[css]").each(function(){
        $(this).attr("style",$(this).attr("css"));
      });
    });
    </script>
  </body>
</html>

