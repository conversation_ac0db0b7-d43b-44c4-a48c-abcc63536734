<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
<meta name="pinterest" content="nopin">
<meta name="viewport" content="width=device-width,minimum-scale=1,initial-scale=1">
<meta name="generator" content="Hugo 0.56.3" />



<link rel="canonical" href="https://snail007.goproxyauth.com/goproxy/page/free_vs_commercial/">


    <link href="//maxcdn.bootstrapcdn.com/font-awesome/4.5.0/css/font-awesome.min.css" rel="stylesheet">
    <link rel="stylesheet" href="//maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css">
    <link rel="stylesheet" href="//cdnjs.cloudflare.com/ajax/libs/highlight.js/8.4/styles/solarized_dark.min.css">
    <title>GOPROXY免费版VS商业版 - GOPROXY IN ACTION</title>
    
<meta name="description" content="GOPROXY 免费版VS商业版">

<meta property="og:title" content="GOPROXY免费版VS商业版 - GOPROXY IN ACTION">
<meta property="og:type" content="article">
<meta property="og:url" content="https://snail007.goproxyauth.com/goproxy/page/free_vs_commercial/">
<meta property="og:image" content="https://snail007.goproxyauth.com/goproxy/images/default.png">
<meta property="og:site_name" content="GOPROXY IN ACTION">
<meta property="og:description" content="GOPROXY 免费版VS商业版">
<meta property="og:locale" content="zh_CN">

<meta name="twitter:card" content="summary_large_image">
<meta name="twitter:site" content="GOPROXY IN ACTION">
<meta name="twitter:url" content="https://snail007.goproxyauth.com/goproxy/page/free_vs_commercial/">
<meta name="twitter:title" content="GOPROXY免费版VS商业版 - GOPROXY IN ACTION">
<meta name="twitter:description" content="GOPROXY 免费版VS商业版">
<meta name="twitter:image" content="https://snail007.goproxyauth.com/goproxy/images/default.png">


<script type="application/ld+json">
  {
    "@context": "http://schema.org",
    "@type": "NewsArticle",
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id":"https:\/\/snail007.goproxyauth.com\/goproxy\/"
    },
    "headline": "GOPROXY免费版VS商业版 - GOPROXY IN ACTION",
    "image": {
      "@type": "ImageObject",
      "url": "https:\/\/snail007.goproxyauth.com\/goproxy\/images\/default.png",
      "height": 800,
      "width": 800
    },
    "datePublished": "2019-06-26T13:19:15JST",
    "dateModified": "2019-06-26T13:19:15JST",
    "author": {
      "@type": "Person",
      "name": "GOPROXY IN ACTION"
    },
    "publisher": {
      "@type": "Organization",
      "name": "GOPROXY IN ACTION",
      "logo": {
        "@type": "ImageObject",
        "url": "https:\/\/snail007.goproxyauth.com\/goproxy\/images/logo.png",
        "width": 600,
        "height": 60
      }
    },
    "description": "GOPROXY 免费版VS商业版"
  }
</script>


    <link href="https://snail007.goproxyauth.com/goproxy/css/styles.css" rel="stylesheet">
    

  </head>

  <body>
    
    
    

    <header class="l-header">
      <nav class="navbar navbar-default">
        <div class="container">
          <div class="navbar-header">
            <button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#navbar" aria-expanded="false">
              <span class="sr-only">Toggle navigation</span>
              <span class="icon-bar"></span>
              <span class="icon-bar"></span>
              <span class="icon-bar"></span>
            </button>
            <a class="navbar-brand" href="https://snail007.goproxyauth.com/goproxy/">GOPROXY IN ACTION</a>
          </div>

          
          <div id="navbar" class="collapse navbar-collapse">
            
            <ul class="nav navbar-nav navbar-right">
              
              
              <li><a href="https://github.com/snail007/goproxy/releases/">下载</a></li>
              
              
              
              <li><a href="https://snail007.goproxyauth.com/goproxy/categories/goproxy%E6%89%8B%E5%86%8C/">实战</a></li>
              
              
              
              <li><a href="https://snail007.goproxyauth.com/goproxy/manual/zh/#/">手册</a></li>
              
              
              
              <li><a href="/goproxy/about/">关于</a></li>
              
              
            </ul>
            
          </div>
          

        </div>
      </nav>
    </header>

    <main>
      <div class="container">
        
<div class="row">
  <div class="col-md-8">

    <nav class="p-crumb">
      <ol class="breadcrumb">
        <li><a href="https://snail007.goproxyauth.com/goproxy/"><i class="fa fa-home" aria-hidden="true"></i></a></li>
        
        <li itemscope="" itemtype="http://data-vocabulary.org/Breadcrumb"><a href="https://snail007.goproxyauth.com/goproxy/page/" itemprop="url"><span itemprop="title">page</span></a></li>
        
        <li class="active">GOPROXY免费版VS商业版</li>
      </ol>
    </nav>

    <article class="single">
  <header>
    <ul class="p-facts">
      <li><i class="fa fa-calendar" aria-hidden="true"></i><time datetime="2019-06-26T13:19:15JST">Jun 26, 2019</time></li>
      <li><i class="fa fa-bookmark" aria-hidden="true"></i><a href="https://snail007.goproxyauth.com/goproxy/page/">page</a></li>
      
    </ul>

    <h1 class="title">GOPROXY免费版VS商业版</h1>
  </header>

  

  <div class="article-body">

<h4 id="目前-goproxy-和-sdk-提供免费版和商业版-功能对比如下">目前，GOPROXY 和 SDK 提供免费版和商业版，功能对比如下。</h4>

<table>
<thead>
<tr>
<th align="left">　　　　　　　　</th>
<th align="center">　　　免费版　　　</th>
<th align="center">　　　商业版　　　</th>
</tr>
</thead>

<tbody>
<tr>
<td align="left"><code>TCP转发</code></td>
<td align="center">√</td>
<td align="center">√</td>
</tr>

<tr>
<td align="left"><code>UDP转发</code></td>
<td align="center">√</td>
<td align="center">√</td>
</tr>

<tr>
<td align="left"><code>HTTP(S)代理</code></td>
<td align="center">√</td>
<td align="center">√</td>
</tr>

<tr>
<td align="left"><code>Socks5代理</code></td>
<td align="center">√</td>
<td align="center">√</td>
</tr>

<tr>
<td align="left"><code>Socks5代理UDP支持</code></td>
<td align="center">√</td>
<td align="center">√</td>
</tr>

<tr>
<td align="left"><code>SPS综合代理</code></td>
<td align="center">√</td>
<td align="center">√</td>
</tr>

<tr>
<td align="left"><code>SPS上级认证</code></td>
<td align="center">√</td>
<td align="center">√</td>
</tr>

<tr>
<td align="left"><code>内网穿透</code></td>
<td align="center">√</td>
<td align="center">√</td>
</tr>

<tr>
<td align="left"><code>DNS代理，防污染，独立DNS服务</code></td>
<td align="center">√</td>
<td align="center">√</td>
</tr>

<tr>
<td align="left"><code>HTTP(S)代理认证，认证API，负载均衡</code></td>
<td align="center">×</td>
<td align="center">√</td>
</tr>

<tr>
<td align="left"><code>SOCKS5代理认证，认证API，负载均衡</code></td>
<td align="center">×</td>
<td align="center">√</td>
</tr>

<tr>
<td align="left"><code>SPS代理认证，认证API，负载均衡</code></td>
<td align="center">×</td>
<td align="center">√</td>
</tr>

<tr>
<td align="left"><code>STOP&amp;ONLY黑白名单</code></td>
<td align="center">×</td>
<td align="center">√</td>
</tr>

<tr>
<td align="left"><code>客户端IP黑白名单</code></td>
<td align="center">×</td>
<td align="center">√</td>
</tr>

<tr>
<td align="left"><code>检查更新，失败退出</code></td>
<td align="center">×</td>
<td align="center">×</td>
</tr>

<tr>
<td align="left"><code>单机进程数量限制</code></td>
<td align="center">×</td>
<td align="center">×</td>
</tr>

<tr>
<td align="left"><code>手册完整功能</code></td>
<td align="center">×</td>
<td align="center">√</td>
</tr>

<tr>
<td align="left"><code>需要联网认证</code></td>
<td align="center">×</td>
<td align="center">√</td>
</tr>

<tr>
<td align="left"><code>限速功能</code></td>
<td align="center">×</td>
<td align="center">√</td>
</tr>

<tr>
<td align="left"><code>http/socks5/tcp代理指定出口IP</code></td>
<td align="center">×</td>
<td align="center">√</td>
</tr>

<tr>
<td align="left"><code>目标连接重定向</code></td>
<td align="center">×</td>
<td align="center">√</td>
</tr>

<tr>
<td align="left"><code>docker</code></td>
<td align="center">√</td>
<td align="center">√</td>
</tr>

<tr>
<td align="left"><code>免费更新至更多功能的商业版</code></td>
<td align="center">×</td>
<td align="center">√</td>
</tr>

<tr>
<td align="left"><code>http(s)\socks\sps，上级ws/wss支持路径和独立加密</code></td>
<td align="center">×</td>
<td align="center">√</td>
</tr>

<tr>
<td align="left"><code>用户限速，IP限速</code></td>
<td align="center">×</td>
<td align="center">√</td>
</tr>

<tr>
<td align="left"><code>用户限连接数，IP限连接数</code></td>
<td align="center">×</td>
<td align="center">√</td>
</tr>

<tr>
<td align="left"><code>API动态选择上级代理</code></td>
<td align="center">×</td>
<td align="center">√</td>
</tr>

<tr>
<td align="left"><code>socks5/http(s)/sps/tcp/udp代理监听支持端口范围</code></td>
<td align="center">×</td>
<td align="center">√</td>
</tr>
</tbody>
</table>

<h4 id="商业版授权方式分为两种">商业版授权方式分为两种</h4>

<ol>
<li><p>程序和机器绑定，单个机器方式授权，针对<code>机器码</code>收费。</p></li>

<li><p>程序和不和机器绑定，授权码方式授权，针对<code>授权码</code>收费。</p></li>
</ol>

<h4 id="商业版使用">商业版使用</h4>

<ol>
<li><p>去平台授权平台 <a href="https://gpm.goproxyauth.com/?lang=zh-CN">点击进入</a> 注册一个用户。</p></li>

<li><p>获取机器的机器码（<code>机器码</code>授权方式）：</p>

<ul>
<li>如果是<code>goproxy商业版</code>，那么首次执行：<code>proxy http</code>， 并<strong>等待程序自己退出，不要手动结束程序</strong>，
然后会在当前目录下面生成id.txt文件，里面是当前机器的机器码，另外命令行也会显示机器码<code>code:</code>后面的东西，注意不包含空格。</li>
<li>如果是<code>proxy-admin 商业版 控制面板</code>和<code>proxy-admin vip 控制面板</code>，那么首次执行：<code>proxy-admin</code>不带任何参数， 并*
*等待程序自己退出，不要手动结束程序**，
然后会在当前目录和下面的路径生成id.txt文件，Linux位于: <code>/etc/gpa/id.txt</code>，Windows位于: <code>C:\gpa\id.txt</code>
，里面内容是当前机器的机器码。</li>
<li>另外直接执行命<code>proxy-admin</code>不带任何参数，也会显示机器码，即<code>code:</code>后面的东西，注意不包含空格。</li>
<li>然后在授权后台，在线自助购买，购买机器码名额。然后点击菜单<code>我的机器码</code>，点击列表操作列的<code>修改</code>
,把id.txt内的机器码填入到机器码输入框里面，然后保存即可完成授权。</li>
<li>卡密方式，此方式可以大批量添加机器码，如需要此方式，可联系 <code><EMAIL></code>
购买卡密，去授权平台后台点击菜单<code>添加机器码</code>输入<code>卡密</code>完成机器码购买。</li>
</ul></li>

<li><p><code>授权码</code>方式授权，目前只有<code>goproxy商业版</code>有此授权方式。首先点击进入 <a href="https://gpm.goproxyauth.com/">授权后台</a>
，登录后，点击菜单，我的授权码<code>-&gt;</code>在线购买授权码，
然后会在<code>我的授权码列表</code>看见购买的<code>授权码</code>。 然后就是启动<code>proxy</code>的时候加上授权码参数<code>--authcode 授权码</code>
启动程序即可，也可以设置环境变量<code>LIC_AUTHCODE</code>内容为授权码，
如果没有使用参数<code>--authcode</code>，程序尝试从环境变量<code>LIC_AUTHCODE</code>获取授权码。</p></li>

<li><p>商业版下载，进入对应项目的首页，点击中文介绍，有详细的下载安装说明。</p>

<ul>
<li>下载文件列表中文件名后缀是：_commercial.tar.gz的为商业版。请根据自己系统情况下载。
-
所有软件项目地址：<a href="https://snail007.goproxyauth.com/goproxy/manual/zh/#/?id=%e5%85%8d%e8%b4%b9%e7%89%88%e3%80%81%e5%95%86%e4%b8%9a%e7%89%88%e8%af%b4%e6%98%8e">点击进入</a></li>
</ul></li>

<li><p>注意：不同名字的软件，是不同的软件，不同的软件需要各自购买授权，所以要确保购买的软件和安装的软件是一致的。</p></li>

<li><p>完全手册：<a href="https://github.com/snail007/goproxy/blob/master/README_ZH.md">点击进入</a><br />
新版手册：<a href="https://snail007.github.io/goproxy/categories/goproxy%E6%89%8B%E5%86%8C/">点击进入</a></p></li>
</ol>

<h4 id="proxyadmin-授权码">proxyadmin 授权码</h4>

<p><code>proxyadmin_coml</code>(商业版) 和 <code>proxyadmin_vip</code>(VIP版本)也支持<code>授权码</code>方式授权，但是授权码使用方式和goproxy不一样。</p>

<p>安装<code>proxyadmin_coml</code>(商业版)或<code>proxyadmin_vip</code>(VIP版本)后，系统类型不一样，会在下面路径生成一个authcode.txt的空文件，只需要把购买的<code>授权码</code>写入该文件，然后执行<code>proxy-admin restart</code>重启程序即可。</p>

<ul>
<li>Linux：<code>/etc/gpa/authcode.txt</code></li>
<li>macOS：<code>/etc/gpa/authcode.txt</code></li>
<li>Windows：<code>C:\gpa\authcode.txt</code></li>
</ul>

<h4 id="提醒">提醒</h4>

<p>商业版只会在启动的时候联网检查一次授权，后期不会再联网检查，只会在<code>授权码</code>、<code>机器码</code>到期的时候再检查一次，如果48小时内无法成功检查授权程序会退出。
如果系统发现用户恶意使用或传播自己的<code>授权码</code>、<code>机器码</code>，官方有权终止其使用，并不负任何责任。</p>

<h4 id="购买商业版">购买商业版</h4>

<ol>
<li><p>购买商业版，授权后台可以在线购买，特殊需求发送邮件到：<code><EMAIL></code> 。</p></li>

<li><p>除了联系管理员购买卡密，还可以在线购买机器码。首先登录授权平台，点击菜单：机器码管理-&gt;在线购买，可以完成少量机器码的购买。如果需要一次购买很多，可以根据网页提示联系管理员。</p></li>
</ol>

<h4 id="商业版免费试用">商业版免费试用</h4>

<ol>
<li><p>购买商业版之前你可以免费试用7天，以用来确定GOPROXY商业版功能是否满足你的需求。</p></li>

<li><p>试用步骤：登录<a href="https://gpm.goproxyauth.com/?lang=zh-CN">授权平台</a> -&gt; 机器码管理 -&gt; 在线购买 -&gt; 点击&rdquo;开始试用&rdquo;。</p></li>

<li><p>proxy-vip控制面板，proxy-admin控制面板商业版均不支持试用，因为它们和免费版proxy-vip界面和操作一样。
proxy-admin控制面板功能和goproxy商业版功能一样，区别是</p>

<h4 id="商业版用户须知">商业版用户须知</h4></li>
</ol>

<p>1、官方免费提供首次安装指导。</p>

<p>2、商业版用户可以提一些商业功能意见，官方会针对意见的合理性等情况进行评估，如果意见被采纳，在未来新版本中会加入。</p>

<p>3、商业版用户可以免费更新至功能更多的商业版新版。</p>

<p>4、没有技术支持。</p>

<h4 id="不能访问授权的解决办法">不能访问授权的解决办法</h4>

<p>商业版必须可以在线访问授权才能使用，如果您在使用proxy过程中出现服务器可以访问互联网，但是proxy提示访问授权信息失败。可以按着以下方法解决。</p>

<h5 id="1-如果您的proxy机器可以访问互联网-由于机器dns可能不能正确解析更新域名-可以指定dns启动proxy">1.如果您的proxy机器可以访问互联网，由于机器DNS可能不能正确解析更新域名，可以指定DNS启动proxy。</h5>

<p>Linux启动命令示例：</p>

<pre><code class="language-shell">LIC_DNS=8.8.4.4:53 ./proxy http -p :9090
</code></pre>

<p>Windows启动命令示例</p>

<pre><code class="language-shell">set LIC_DNS=8.8.4.4:53
./proxy.exe http -p :9090
</code></pre>

<h5 id="2-如果proxy是在内网环境不能直接访问互联网-你可以在能够访问互联网的机器上启动一个http代理-然后给proxy使用">2.如果proxy是在内网环境不能直接访问互联网，你可以在能够访问互联网的机器上启动一个http代理，然后给proxy使用。</h5>

<p>比如： proxy在192.168.1.10,不能直接访问互联网。</p>

<p>在192.168.1.200机器可以访问互联网,那么可以在192.168.1.200机器启动：</p>

<p><code>./proxy http -p :8080 -q 8.8.4.4:53</code></p>

<p>在192.168.1.10上面启动命令：</p>

<p><code>LIC_PROXY=http://192.168.1.200:8080 ./proxy http -p :9090</code></p>

<h5 id="3-如果内网有现成的http代理-那么2步骤里面不需要自己启动一个代理-lic-proxy直接使用现成的http代理即可">3.如果内网有现成的http代理，那么2步骤里面不需要自己启动一个代理，LIC_PROXY直接使用现成的http代理即可。</h5>
</div>

  <footer class="article-footer">
    
    
    
    <section class="bordered">
      <header>
        <div class="panel-title">CATEGORIES</div>
      </header>
      <div>
        <ul class="p-terms">
          
          <li><a href="https://snail007.goproxyauth.com/goproxy/categories/%E9%BB%98%E8%AE%A4%E5%88%86%E7%B1%BB/">默认分类</a></li>
          
        </ul>
      </div>
    </section>
    
    
    
    <section class="bordered">
      <header>
        <div class="panel-title">TAGS</div>
      </header>
      <div>
        <ul class="p-terms">
          
          <li><a href="https://snail007.goproxyauth.com/goproxy/tags/%E5%95%86%E4%B8%9A%E7%89%88/">商业版</a></li>
          
        </ul>
      </div>
    </section>
    
    
  </footer>

</article>


    <div id="disqus_thread"></div>
<script type="application/javascript">
    var disqus_config = function () {
    
    
    
    };
    (function() {
        if (["localhost", "127.0.0.1"].indexOf(window.location.hostname) != -1) {
            document.getElementById('disqus_thread').innerHTML = 'Disqus comments not available by default when the website is previewed locally.';
            return;
        }
        var d = document, s = d.createElement('script'); s.async = true;
        s.src = '//' + "goproxy" + '.disqus.com/embed.js';
        s.setAttribute('data-timestamp', +new Date());
        (d.head || d.body).appendChild(s);
    })();
</script>
<noscript>Please enable JavaScript to view the <a href="https://disqus.com/?ref_noscript">comments powered by Disqus.</a></noscript>
<a href="https://disqus.com" class="dsq-brlink">comments powered by <span class="logo-disqus">Disqus</span></a>
  </div>

  <div class="col-md-4">
    
<aside class="l-sidebar">

  <section class="panel panel-default">
    <div class="panel-heading">
      <div class="panel-title">LATESTS</div>
    </div>
    <div class="list-group">
      
      <a href="https://snail007.goproxyauth.com/goproxy/usage/first/" class="list-group-item">手册前言（使用必读）</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/page/free_vs_commercial/" class="list-group-item">GOPROXY免费版VS商业版</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/page/faq/goproxy%E5%B8%B8%E8%A7%81%E9%97%AE%E9%A2%98%E8%A7%A3%E7%AD%94/" class="list-group-item">常见问题解答</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/posts/domain-cf/" class="list-group-item">域名解析 域名控制台&#43;极客&#43;cloudflare 的配置方法</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/posts/windows-global-proxy-using-dns/" class="list-group-item">在windows下实现基于dns的自动全局代理</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/usage/tcp/" class="list-group-item">TCP代理</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/posts/cloudflare/" class="list-group-item">Cloudflare使用简明教程</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/posts/http-nat-cdn/" class="list-group-item">内网穿透也能用CDN？？？！</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/page/free_vs_commercial_en/" class="list-group-item">GOPROXY Free Version VS Commercial Version</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/posts/http_cdn_ws/" class="list-group-item">代理之套用CDN</a>
      
    </div>
  </section>

  
  <section class="panel panel-default">
    <div class="panel-heading">
      <div class="panel-title">CATEGORY</div>
    </div>
    <div class="list-group">
      
      <a href="https://snail007.goproxyauth.com/goproxy/categories/%E9%BB%98%E8%AE%A4%E5%88%86%E7%B1%BB" class="list-group-item">默认分类</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/categories/goproxy%E6%89%8B%E5%86%8C" class="list-group-item">goproxy手册</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/categories/%E6%9E%B6%E6%9E%84%E8%A7%A3%E8%AF%B4" class="list-group-item">架构解说</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/categories/%E7%BB%86%E8%AF%B4%E5%B1%82%E7%BA%A7" class="list-group-item">细说层级</a>
      
    </div>
  </section>
  
  <section class="panel panel-default">
    <div class="panel-heading">
      <div class="panel-title">TAG</div>
    </div>
    <div class="list-group">
      
      <a href="https://snail007.goproxyauth.com/goproxy/tags/cdn" class="list-group-item">cdn</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/tags/ws" class="list-group-item">ws</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/tags/cloudflare" class="list-group-item">cloudflare</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/tags/%E5%95%86%E4%B8%9A%E7%89%88" class="list-group-item">商业版</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/tags/commercial" class="list-group-item">commercial</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/tags/domain" class="list-group-item">domain</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/tags/tcp" class="list-group-item">tcp</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/tags/%E5%85%A8%E5%B1%80%E4%BB%A3%E7%90%86" class="list-group-item">全局代理</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/tags/%E5%86%85%E7%BD%91%E7%A9%BF%E9%80%8F" class="list-group-item">内网穿透</a>
      
    </div>
  </section>
  

</aside>


  </div>
</div>

      </div>
    </main>

    <footer class="l-footer">
      <div class="container">
        <p><span class="h-logo">&copy; GOPROXY IN ACTION</span></p>
      </div>
    </footer>

    <script src="//code.jquery.com/jquery-3.1.1.min.js"></script>
    <script src="//maxcdn.bootstrapcdn.com/bootstrap/3.3.7/js/bootstrap.min.js"></script>
    <script src="//cdnjs.cloudflare.com/ajax/libs/highlight.js/8.4/highlight.min.js"></script>
    <script>hljs.initHighlightingOnLoad();</script>
    <script>
    $(function(){
      $("img[css]").each(function(){
        $(this).attr("style",$(this).attr("css"));
      });
    });
    </script>
  </body>
</html>

