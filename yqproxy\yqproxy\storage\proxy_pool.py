"""
代理池存储逻辑
组合Redis客户端，提供代理池相关的存储操作
"""

import random
from typing import Dict, List, Optional, Tuple
from yqproxy.utils import get_logger

logger = get_logger(__name__)

# 依赖models层和settings
from yqproxy.models import (
    Proxy,
    SUPPORTED_PROTOCOLS
)
from yqproxy.models.constants import (
    PROXY_SCORE_MAX,
    PROXY_SCORE_MIN
)
from yqproxy.settings import (
    REDIS_PIPELINE_ENABLED,
    REDIS_PIPELINE_BATCH_SIZE,
    REDIS_PIPELINE_MAX_SIZE
)
from yqproxy.storage.redis_client import RedisClient


class ProxyPool:
    """
    代理池存储管理器
    组合Redis客户端，提供代理池相关的存储逻辑
    """
    
    def __init__(self, redis_client: RedisClient, key_prefix: str = "proxies"):
        """
        初始化代理池存储管理器
        
        Args:
            redis_client: Redis客户端实例
            key_prefix: Redis键前缀
        """
        self.redis = redis_client
        self.key_prefix = key_prefix
    
    def _get_key(self, protocol: str) -> str:
        """
        生成协议对应的存储键
        
        Args:
            protocol: 协议类型
            
        Returns:
            Redis存储键
        """
        return f"{self.key_prefix}:{protocol}"
    
    def _get_storage_key(self, protocol: str) -> str:
        """
        生成协议对应的存储键（兼容接口）
        
        Args:
            protocol: 协议类型
            
        Returns:
            Redis存储键
        """
        return self._get_key(protocol)
    
    def get_all_protocol_keys(self) -> List[str]:
        """
        获取所有协议的存储键
        
        Returns:
            所有协议键列表
        """
        return [self._get_storage_key(protocol) for protocol in SUPPORTED_PROTOCOLS]
    
    # ==================== 代理操作 ====================
    
    async def add_proxy(self, proxy: Proxy) -> bool:
        """
        添加单个代理
        
        Args:
            proxy: 代理对象
            
        Returns:
            是否添加成功
        """
        if not proxy.is_valid():
            logger.debug(f"无效代理，跳过: {proxy}")
            return False
        
        key = self._get_storage_key(proxy.protocol)
        
        try:
            result = await self.redis.zadd(key, {proxy.address: proxy.score}, nx=True)
            if result > 0:
                logger.debug(f"成功添加代理: {proxy.address} -> {key}")
            return result > 0
        except Exception as e:
            logger.error(f"添加代理失败 {proxy}: {e}")
            return False
    
    async def add_proxies_batch(self, proxies: List[Proxy]) -> Dict[str, int]:
        """
        批量添加代理（使用pipeline优化）
        
        Args:
            proxies: 代理列表
            
        Returns:
            各协议的添加结果 {protocol: count}
        """
        if not proxies:
            return {}
        
        # 按协议分组有效代理
        protocol_groups: Dict[str, List[Proxy]] = {}
        for proxy in proxies:
            if proxy.is_valid():
                protocol_groups.setdefault(proxy.protocol, []).append(proxy)
            else:
                logger.debug(f"无效代理，跳过: {proxy}")
        
        if not protocol_groups:
            return {}
        
        # 如果未启用pipeline或代理数量较少，使用传统方式
        total_count = sum(len(group) for group in protocol_groups.values())
        if not REDIS_PIPELINE_ENABLED or total_count < 10:
            results = {}
            for protocol, proxy_list in protocol_groups.items():
                key = self._get_storage_key(protocol)
                mapping = {proxy.address: proxy.score for proxy in proxy_list}
                try:
                    count = await self.redis.zadd(key, mapping, nx=True)
                    results[protocol] = count
                    logger.debug(f"添加代理到 {key}: {count}/{len(proxy_list)}")
                except Exception as e:
                    logger.error(f"添加代理失败 {key}: {e}")
                    results[protocol] = 0
            return results
        
        # 使用pipeline批量操作
        results = {}
        for protocol, proxy_list in protocol_groups.items():
            key = self._get_storage_key(protocol)
            added_count = 0
            
            # 分批处理以避免单个pipeline过大
            for i in range(0, len(proxy_list), REDIS_PIPELINE_BATCH_SIZE):
                batch = proxy_list[i:i + min(REDIS_PIPELINE_BATCH_SIZE, REDIS_PIPELINE_MAX_SIZE)]
                
                # 构建批量操作
                operations = []
                mapping = {proxy.address: proxy.score for proxy in batch}
                operations.append((key, mapping, True, False))  # nx=True, xx=False
                
                try:
                    # 执行批量操作
                    batch_results = await self.redis.batch_zadd(operations)
                    added_count += batch_results[0] if batch_results else 0
                except Exception as e:
                    logger.error(f"批量添加代理失败 {key}: {e}")
            
            results[protocol] = added_count
            logger.debug(f"批量添加代理到 {protocol}: {added_count}/{len(proxy_list)}")
        
        return results
    
    async def get_random_proxy(self, protocol: str = None) -> Optional[Proxy]:
        """
        获取随机代理（优先获取高分代理）
        
        Args:
            protocol: 协议类型，None表示从所有协议中随机选择
            
        Returns:
            代理对象或None
        """
        if protocol:
            protocols_to_try = [protocol]
        else:
            # 随机化协议顺序
            protocols_to_try = SUPPORTED_PROTOCOLS.copy()
            random.shuffle(protocols_to_try)
        
        for proto in protocols_to_try:
            key = self._get_storage_key(proto)
            
            try:
                # 优先获取满分代理
                addresses = await self.redis.zrangebyscore(key, PROXY_SCORE_MAX, PROXY_SCORE_MAX)
                if not addresses:
                    # 如果没有满分代理，获取中等分数代理
                    addresses = await self.redis.zrangebyscore(key, PROXY_SCORE_MAX // 2, PROXY_SCORE_MAX - 1)
                
                if addresses:
                    address = random.choice(addresses)
                    return Proxy.from_address(address, protocol=proto)
                    
            except Exception as e:
                logger.error(f"获取随机代理失败 {key}: {e}")
        
        return None
    
    async def get_proxies_by_score(self, protocol: str, min_score: int = PROXY_SCORE_MIN,
                                  max_score: int = PROXY_SCORE_MAX, limit: int = None) -> List[Proxy]:
        """
        按分数范围获取代理
        
        Args:
            protocol: 协议类型
            min_score: 最小分数
            max_score: 最大分数
            limit: 限制数量
            
        Returns:
            代理列表
        """
        key = self._get_storage_key(protocol)
        
        try:
            # 如果指定了limit，需要同时提供start和num参数
            if limit is not None:
                addresses = await self.redis.zrangebyscore(key, min_score, max_score, start=0, num=limit)
            else:
                # 不传start和num参数，获取所有匹配的成员
                addresses = await self.redis.zrangebyscore(key, min_score, max_score)
            
            proxies = []
            for address in addresses:
                proxy = Proxy.from_address(address, protocol=protocol)
                if proxy:
                    proxies.append(proxy)
            
            return proxies
            
        except Exception as e:
            logger.error(f"按分数获取代理失败 {key}: {e}")
            return []
    
    async def get_all_proxies(self, protocol: str) -> List[Proxy]:
        """
        获取指定协议的所有代理
        
        Args:
            protocol: 协议类型
            
        Returns:
            代理列表
        """
        return await self.get_proxies_by_score(protocol)
    
    # ==================== 代理状态管理 ====================
    
    async def increase_score(self, proxy: Proxy, amount: int = 1) -> Optional[float]:
        """
        增加代理分数
        
        Args:
            proxy: 代理对象
            amount: 增加的分数
            
        Returns:
            新分数或None
        """
        key = self._get_storage_key(proxy.protocol)
        
        try:
            new_score = await self.redis.zincrby(key, amount, proxy.address)
            # 确保不超过最大分数
            if new_score > PROXY_SCORE_MAX:
                await self.redis.zadd(key, {proxy.address: PROXY_SCORE_MAX}, xx=True)
                new_score = PROXY_SCORE_MAX
            
            # 移除DEBUG日志，保持输出简洁
            # logger.debug(f"代理分数增加: {proxy.address} +{amount} -> {new_score}")
            return new_score
        except Exception as e:
            logger.error(f"增加代理分数失败 {proxy}: {e}")
            return None
    
    async def decrease_score(self, proxy: Proxy, amount: int = 1) -> Optional[float]:
        """
        降低代理分数，分数过低时自动删除
        
        Args:
            proxy: 代理对象
            amount: 降低的分数
            
        Returns:
            新分数或None（已删除）
        """
        key = self._get_storage_key(proxy.protocol)
        
        try:
            new_score = await self.redis.zincrby(key, -amount, proxy.address)
            
            # 分数过低时删除代理
            if new_score <= PROXY_SCORE_MIN:
                await self.redis.zrem(key, proxy.address)
                # 移除DEBUG日志，保持输出简洁
                # logger.debug(f"代理分数过低，已删除: {proxy.address} -> {new_score}")
                return None
            
            # 移除DEBUG日志，保持输出简洁
            # logger.debug(f"代理分数降低: {proxy.address} -{amount} -> {new_score}")
            return new_score
        except Exception as e:
            logger.error(f"降低代理分数失败 {proxy}: {e}")
            return None
    
    async def batch_update_scores(self, updates: List[Tuple[Proxy, int]]) -> List[Optional[float]]:
        """
        批量更新代理分数（使用pipeline）
        
        Args:
            updates: 更新列表，每个元素为 (proxy, amount)
                - proxy: 代理对象
                - amount: 分数变化量（正数增加，负数减少）
            
        Returns:
            新分数列表，删除的代理返回None
        """
        if not updates:
            return []
        
        # 如果未启用pipeline或更新数量较少，使用传统方式
        if not REDIS_PIPELINE_ENABLED or len(updates) < 10:
            results = []
            for proxy, amount in updates:
                if amount > 0:
                    score = await self.increase_score(proxy, amount)
                else:
                    score = await self.decrease_score(proxy, abs(amount))
                results.append(score)
            return results
        
        # 分批处理
        all_results = []
        for batch_start in range(0, len(updates), REDIS_PIPELINE_BATCH_SIZE):
            batch_end = min(batch_start + REDIS_PIPELINE_BATCH_SIZE, len(updates))
            batch_updates = updates[batch_start:batch_end]
            
            # 构建批量操作
            operations = []
            for proxy, amount in batch_updates:
                key = self._get_storage_key(proxy.protocol)
                operations.append((key, amount, proxy.address))
            
            try:
                # 批量执行分数更新
                new_scores = await self.redis.batch_zincrby(operations)
                
                # 处理超出范围的分数
                result_scores = []
                cleanup_operations = []
                max_score_operations = []
                
                for i, (proxy, amount) in enumerate(batch_updates):
                    new_score = new_scores[i]
                    key = self._get_storage_key(proxy.protocol)
                    
                    if new_score <= PROXY_SCORE_MIN:
                        # 需要删除
                        cleanup_operations.append((key, [proxy.address]))
                        result_scores.append(None)
                        # 移除DEBUG日志，保持输出简洁
                        # logger.debug(f"批量更新：代理分数过低，标记删除: {proxy.address}")
                    elif new_score > PROXY_SCORE_MAX:
                        # 需要设置为最大分数
                        max_score_operations.append((key, {proxy.address: PROXY_SCORE_MAX}, False, True))
                        result_scores.append(PROXY_SCORE_MAX)
                        # 移除DEBUG日志，保持输出简洁
                        # logger.debug(f"批量更新：代理分数设为最大: {proxy.address}")
                    else:
                        result_scores.append(new_score)
                
                # 批量删除低分代理
                if cleanup_operations:
                    await self.redis.batch_zrem(cleanup_operations)
                
                # 批量设置最大分数
                if max_score_operations:
                    await self.redis.batch_zadd(max_score_operations)
                
                all_results.extend(result_scores)
                
            except Exception as e:
                logger.error(f"批量更新代理分数失败: {e}")
                all_results.extend([None] * len(batch_updates))
        
        return all_results
    
    async def set_max_score(self, proxy: Proxy) -> bool:
        """
        设置代理为最高分数
        
        Args:
            proxy: 代理对象
            
        Returns:
            是否设置成功
        """
        key = self._get_storage_key(proxy.protocol)
        
        try:
            result = await self.redis.zadd(key, {proxy.address: PROXY_SCORE_MAX}, xx=True)
            if result > 0:
                logger.debug(f"代理设置为满分: {proxy.address}")
            return result > 0
        except Exception as e:
            logger.error(f"设置代理满分失败 {proxy}: {e}")
            return False
    
    async def batch_set_max_score(self, proxies: List[Proxy]) -> List[bool]:
        """
        批量设置代理为最高分数（使用pipeline）
        
        Args:
            proxies: 代理列表
            
        Returns:
            设置结果列表
        """
        if not proxies:
            return []
        
        # 构建批量操作
        operations = []
        for proxy in proxies:
            key = self._get_storage_key(proxy.protocol)
            operations.append((key, {proxy.address: PROXY_SCORE_MAX}, False, True))  # xx=True
        
        try:
            results = await self.redis.batch_zadd(operations)
            return [r > 0 for r in results]
        except Exception as e:
            logger.error(f"批量设置满分失败: {e}")
            return [False] * len(proxies)
    
    async def remove_proxy(self, proxy: Proxy) -> bool:
        """
        删除代理
        
        Args:
            proxy: 代理对象
            
        Returns:
            是否删除成功
        """
        key = self._get_storage_key(proxy.protocol)
        
        try:
            result = await self.redis.zrem(key, proxy.address)
            if result > 0:
                logger.debug(f"删除代理: {proxy.address}")
            return result > 0
        except Exception as e:
            logger.error(f"删除代理失败 {proxy}: {e}")
            return False
    
    async def batch_remove_proxies(self, proxies: List[Proxy]) -> Dict[str, int]:
        """
        批量删除代理（使用pipeline）
        
        Args:
            proxies: 代理列表
            
        Returns:
            各协议的删除结果 {protocol: count}
        """
        if not proxies:
            return {}
        
        # 按协议分组
        protocol_groups: Dict[str, List[str]] = {}
        for proxy in proxies:
            protocol_groups.setdefault(proxy.protocol, []).append(proxy.address)
        
        # 构建批量操作
        operations = []
        protocols = []
        for protocol, addresses in protocol_groups.items():
            key = self._get_storage_key(protocol)
            operations.append((key, addresses))
            protocols.append(protocol)
        
        try:
            results_list = await self.redis.batch_zrem(operations)
            
            # 整理结果
            results = {}
            for protocol, removed_count in zip(protocols, results_list):
                results[protocol] = removed_count
                if removed_count > 0:
                    logger.debug(f"批量删除代理 {protocol}: {removed_count}个")
            
            return results
            
        except Exception as e:
            logger.error(f"批量删除代理失败: {e}")
            return {protocol: 0 for protocol in protocol_groups.keys()}
    
    # ==================== 统计和清理 ====================
    
    async def count_proxies(self, protocol: str = None) -> Dict[str, int]:
        """
        统计代理数量
        
        Args:
            protocol: 协议类型，None表示统计所有协议
            
        Returns:
            代理数量统计 {protocol: count}
        """
        if protocol:
            protocols = [protocol]
        else:
            protocols = SUPPORTED_PROTOCOLS
        
        results = {}
        
        for proto in protocols:
            key = self._get_storage_key(proto)
            try:
                count = await self.redis.zcard(key)
                results[proto] = count
            except Exception as e:
                logger.error(f"统计代理数量失败 {key}: {e}")
                results[proto] = 0
        
        return results
    
    async def count_by_score_range(self, protocol: str, min_score: int, max_score: int) -> int:
        """
        统计分数范围内的代理数量
        
        Args:
            protocol: 协议类型
            min_score: 最小分数
            max_score: 最大分数
            
        Returns:
            代理数量
        """
        key = self._get_storage_key(protocol)
        
        try:
            return await self.redis.zcount(key, min_score, max_score)
        except Exception as e:
            logger.error(f"按分数统计代理失败 {key}: {e}")
            return 0
    
    async def remove_by_score_range(self, protocol: str, min_score: int, max_score: int) -> int:
        """
        删除分数范围内的代理
        
        Args:
            protocol: 协议类型
            min_score: 最小分数
            max_score: 最大分数
            
        Returns:
            删除的代理数量
        """
        key = self._get_storage_key(protocol)
        
        try:
            removed = await self.redis.zremrangebyscore(key, min_score, max_score)
            if removed > 0:
                logger.info(f"按分数删除代理 {key}: {removed}个")
            return removed
        except Exception as e:
            logger.error(f"按分数删除代理失败 {key}: {e}")
            return 0
    
    async def cleanup_invalid_proxies(self) -> Dict[str, int]:
        """
        清理无效代理（中等质量代理）
        
        清理策略：
        - 保留 0-10 分：新代理（初始分数10）
        - 清理 11-75 分：中等质量代理
        - 保留 76-99 分：高质量代理
        - 保留 100 分：满分有效代理
        
        Returns:
            各协议清理的代理数量 {protocol: count}
        """
        results = {}
        
        for protocol in SUPPORTED_PROTOCOLS:
            try:
                # 删除分数在11-75之间的中等质量代理
                removed = await self.remove_by_score_range(protocol, 11, 75)
                results[protocol] = removed
            except Exception as e:
                logger.error(f"清理无效代理失败 {protocol}: {e}")
                results[protocol] = 0
        
        total_removed = sum(results.values())
        if total_removed > 0:
            logger.info(f"清理无效代理完成，总计删除: {total_removed}个（分数11-75）")
        
        return results
    
    # ==================== 工具方法 ====================
    
    async def scan_proxies(self, protocol: str, cursor: int = 0, count: int = 100) -> Tuple[int, List[Proxy]]:
        """
        扫描代理（分页获取）
        
        Args:
            protocol: 协议类型
            cursor: 游标位置
            count: 扫描数量
            
        Returns:
            (新游标, 代理列表)
        """
        key = self._get_storage_key(protocol)
        
        try:
            new_cursor, items = await self.redis.zscan(key, cursor=cursor, count=count)
            
            proxies = []
            for address, score in items:
                proxy = Proxy.from_address(address, protocol=protocol, score=int(score))
                if proxy:
                    proxies.append(proxy)
            
            return new_cursor, proxies
            
        except Exception as e:
            logger.error(f"扫描代理失败 {key}: {e}")
            return 0, []
    
    async def exists_proxy(self, proxy: Proxy) -> bool:
        """
        检查代理是否存在
        
        Args:
            proxy: 代理对象
            
        Returns:
            是否存在
        """
        key = self._get_storage_key(proxy.protocol)
        
        try:
            score = await self.redis.zscore(key, proxy.address)
            return score is not None
        except Exception as e:
            logger.error(f"检查代理存在性失败 {proxy}: {e}")
            return False
    
    async def get_proxy_score(self, proxy: Proxy) -> Optional[float]:
        """
        获取代理分数
        
        Args:
            proxy: 代理对象
            
        Returns:
            代理分数或None
        """
        key = self._get_storage_key(proxy.protocol)
        
        try:
            return await self.redis.zscore(key, proxy.address)
        except Exception as e:
            logger.error(f"获取代理分数失败 {proxy}: {e}")
            return None
    
    async def close(self):
        """关闭存储连接"""
        await self.redis.close()