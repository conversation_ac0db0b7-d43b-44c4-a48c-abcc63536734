package main

import (
	"bufio"
	"context"
	"fmt"
	"io"
	"log"
	"math/rand"
	"net"
	"net/http"
	"net/url"
	"os"
	"strconv"
	"strings"
	"sync"
	"time"
)

// ProxyInfo 代理信息结构
type ProxyInfo struct {
	Host     string
	Port     int
	Protocol string
}

// ProxyPool 代理池
type ProxyPool struct {
	proxies []ProxyInfo
	mutex   sync.RWMutex
	index   int
}

// LoadBalancer 负载均衡器
type LoadBalancer struct {
	pool       *ProxyPool
	apiURL     string
	apiKey     string
	updateChan chan struct{}
}

func main() {
	// 从环境变量获取配置
	apiURL := getEnv("YQPROXY_API_URL", "http://localhost:5555")
	apiKey := getEnv("YQPROXY_API_KEY", "")
	proxyPort := getEnv("PROXY_PORT", "7890")
	updateInterval := getEnvInt("UPDATE_INTERVAL", 300) // 默认5分钟

	// 创建负载均衡器
	lb := &LoadBalancer{
		pool:       &ProxyPool{},
		apiURL:     apiURL,
		apiKey:     apiKey,
		updateChan: make(chan struct{}, 1),
	}

	// 启动代理更新协程
	go lb.startProxyUpdater(time.Duration(updateInterval) * time.Second)

	// 初始化代理列表
	lb.updateProxies()

	// 启动HTTP代理服务器
	log.Printf("启动负载均衡代理服务器，监听端口: %s", proxyPort)
	log.Printf("YQProxy API: %s", apiURL)
	
	server := &http.Server{
		Addr:    ":" + proxyPort,
		Handler: http.HandlerFunc(lb.handleRequest),
	}

	if err := server.ListenAndServe(); err != nil {
		log.Fatalf("服务器启动失败: %v", err)
	}
}

// 获取环境变量，如果不存在则返回默认值
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// 获取整数环境变量
func getEnvInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

// 启动代理更新器
func (lb *LoadBalancer) startProxyUpdater(interval time.Duration) {
	ticker := time.NewTicker(interval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			lb.updateProxies()
		case <-lb.updateChan:
			lb.updateProxies()
		}
	}
}

// 更新代理列表
func (lb *LoadBalancer) updateProxies() {
	log.Println("开始更新代理列表...")
	
	var allProxies []ProxyInfo
	protocols := []string{"http", "socks4", "socks5"}
	
	for _, protocol := range protocols {
		proxies := lb.fetchProxiesFromAPI(protocol, 50) // 每个协议获取50个
		allProxies = append(allProxies, proxies...)
	}
	
	lb.pool.mutex.Lock()
	lb.pool.proxies = allProxies
	lb.pool.index = 0
	lb.pool.mutex.Unlock()
	
	log.Printf("代理列表更新完成，共获取 %d 个代理", len(allProxies))
}

// 从API获取代理
func (lb *LoadBalancer) fetchProxiesFromAPI(protocol string, count int) []ProxyInfo {
	apiURL := fmt.Sprintf("%s/batch?protocol=%s&count=%d", lb.apiURL, protocol, count)
	
	req, err := http.NewRequest("GET", apiURL, nil)
	if err != nil {
		log.Printf("创建请求失败: %v", err)
		return nil
	}
	
	if lb.apiKey != "" {
		req.Header.Set("API-KEY", lb.apiKey)
	}
	
	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		log.Printf("请求API失败: %v", err)
		return nil
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != http.StatusOK {
		log.Printf("API返回错误状态码: %d", resp.StatusCode)
		return nil
	}
	
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Printf("读取响应失败: %v", err)
		return nil
	}
	
	return lb.parseProxies(string(body), protocol)
}

// 解析代理列表
func (lb *LoadBalancer) parseProxies(data, protocol string) []ProxyInfo {
	var proxies []ProxyInfo
	scanner := bufio.NewScanner(strings.NewReader(data))
	
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if line == "" {
			continue
		}
		
		parts := strings.Split(line, ":")
		if len(parts) != 2 {
			continue
		}
		
		port, err := strconv.Atoi(parts[1])
		if err != nil {
			continue
		}
		
		proxies = append(proxies, ProxyInfo{
			Host:     parts[0],
			Port:     port,
			Protocol: protocol,
		})
	}
	
	log.Printf("解析到 %d 个 %s 代理", len(proxies), protocol)
	return proxies
}

// 获取下一个代理（轮询）
func (lb *LoadBalancer) getNextProxy() *ProxyInfo {
	lb.pool.mutex.Lock()
	defer lb.pool.mutex.Unlock()
	
	if len(lb.pool.proxies) == 0 {
		return nil
	}
	
	proxy := &lb.pool.proxies[lb.pool.index]
	lb.pool.index = (lb.pool.index + 1) % len(lb.pool.proxies)
	
	return proxy
}

// 获取随机代理
func (lb *LoadBalancer) getRandomProxy() *ProxyInfo {
	lb.pool.mutex.RLock()
	defer lb.pool.mutex.RUnlock()
	
	if len(lb.pool.proxies) == 0 {
		return nil
	}
	
	index := rand.Intn(len(lb.pool.proxies))
	return &lb.pool.proxies[index]
}

// 处理HTTP请求
func (lb *LoadBalancer) handleRequest(w http.ResponseWriter, r *http.Request) {
	if r.Method == http.MethodConnect {
		lb.handleHTTPS(w, r)
	} else {
		lb.handleHTTP(w, r)
	}
}

// 处理HTTPS请求（CONNECT方法）
func (lb *LoadBalancer) handleHTTPS(w http.ResponseWriter, r *http.Request) {
	proxy := lb.getRandomProxy()
	if proxy == nil {
		http.Error(w, "No proxy available", http.StatusServiceUnavailable)
		return
	}
	
	// 连接到上游代理
	proxyAddr := fmt.Sprintf("%s:%d", proxy.Host, proxy.Port)
	proxyConn, err := net.DialTimeout("tcp", proxyAddr, 10*time.Second)
	if err != nil {
		log.Printf("连接代理失败 %s: %v", proxyAddr, err)
		http.Error(w, "Proxy connection failed", http.StatusBadGateway)
		return
	}
	defer proxyConn.Close()
	
	// 向上游代理发送CONNECT请求
	connectReq := fmt.Sprintf("CONNECT %s HTTP/1.1\r\nHost: %s\r\n\r\n", r.Host, r.Host)
	if _, err := proxyConn.Write([]byte(connectReq)); err != nil {
		http.Error(w, "Failed to send CONNECT", http.StatusBadGateway)
		return
	}
	
	// 读取代理响应
	response := make([]byte, 1024)
	n, err := proxyConn.Read(response)
	if err != nil {
		http.Error(w, "Failed to read proxy response", http.StatusBadGateway)
		return
	}
	
	// 检查代理是否成功建立连接
	if !strings.Contains(string(response[:n]), "200") {
		http.Error(w, "Proxy CONNECT failed", http.StatusBadGateway)
		return
	}
	
	// 劫持客户端连接
	hijacker, ok := w.(http.Hijacker)
	if !ok {
		http.Error(w, "Hijacking not supported", http.StatusInternalServerError)
		return
	}
	
	clientConn, _, err := hijacker.Hijack()
	if err != nil {
		http.Error(w, "Failed to hijack connection", http.StatusInternalServerError)
		return
	}
	defer clientConn.Close()
	
	// 发送200响应给客户端
	clientConn.Write([]byte("HTTP/1.1 200 Connection Established\r\n\r\n"))
	
	// 开始双向数据转发
	go lb.copyData(clientConn, proxyConn)
	lb.copyData(proxyConn, clientConn)
}

// 处理HTTP请求
func (lb *LoadBalancer) handleHTTP(w http.ResponseWriter, r *http.Request) {
	proxy := lb.getRandomProxy()
	if proxy == nil {
		http.Error(w, "No proxy available", http.StatusServiceUnavailable)
		return
	}
	
	// 构建代理URL
	var proxyURL *url.URL
	var err error
	
	switch proxy.Protocol {
	case "http":
		proxyURL, err = url.Parse(fmt.Sprintf("http://%s:%d", proxy.Host, proxy.Port))
	case "socks4", "socks5":
		// 对于SOCKS代理，我们需要使用HTTP CONNECT方法
		lb.handleHTTPViaSocks(w, r, proxy)
		return
	default:
		proxyURL, err = url.Parse(fmt.Sprintf("http://%s:%d", proxy.Host, proxy.Port))
	}
	
	if err != nil {
		http.Error(w, "Invalid proxy URL", http.StatusInternalServerError)
		return
	}
	
	// 创建代理客户端
	transport := &http.Transport{
		Proxy: http.ProxyURL(proxyURL),
		DialContext: (&net.Dialer{
			Timeout: 10 * time.Second,
		}).DialContext,
	}
	
	client := &http.Client{
		Transport: transport,
		Timeout:   30 * time.Second,
	}
	
	// 转发请求
	req, err := http.NewRequest(r.Method, r.URL.String(), r.Body)
	if err != nil {
		http.Error(w, "Failed to create request", http.StatusInternalServerError)
		return
	}
	
	// 复制请求头
	for key, values := range r.Header {
		for _, value := range values {
			req.Header.Add(key, value)
		}
	}
	
	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		log.Printf("代理请求失败 %s: %v", proxyURL, err)
		http.Error(w, "Proxy request failed", http.StatusBadGateway)
		return
	}
	defer resp.Body.Close()
	
	// 复制响应头
	for key, values := range resp.Header {
		for _, value := range values {
			w.Header().Add(key, value)
		}
	}
	
	// 设置状态码
	w.WriteHeader(resp.StatusCode)
	
	// 复制响应体
	io.Copy(w, resp.Body)
}

// 通过SOCKS代理处理HTTP请求
func (lb *LoadBalancer) handleHTTPViaSocks(w http.ResponseWriter, r *http.Request, proxy *ProxyInfo) {
	// 这里简化处理，直接返回错误
	// 实际实现需要SOCKS客户端库
	http.Error(w, "SOCKS proxy not implemented for HTTP", http.StatusNotImplemented)
}

// 数据复制
func (lb *LoadBalancer) copyData(dst, src net.Conn) {
	defer dst.Close()
	defer src.Close()
	io.Copy(dst, src)
}
