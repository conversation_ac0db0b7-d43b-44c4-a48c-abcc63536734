proxy更新日志

v7.5
1.http(s)\socks\sps 增加了本地监听单向tls支持.
2.socks5协议兼容了更多不标准的客户端.
3.可以使用第三方安卓客户端对接proxy的socks5服务了,支持认证.
4.修复了多处不能正常使用ipv6的bug.

v7.4
1.优化了命令行kcp参数的设置,help命令不再强制显示,只有拥有kcp功能的模块才会显示.
2.内部增加了插件机制,拓展功能十分方便.
3.重构了对配置文件的解析,不再限制参数格式,书写更简单.
4.内网穿透增加了http协议的增强支持,增加了--http-host参数,可以强制设置http头部的HOST字段值.
 HTTP请求客户端会使用server的ip和端口去设置HOST字段,但是与期望的后端实际HOST不一样,这样就造成了tcp是通的,
 但后端依赖HOST字段定位虚拟主机就不能工作.现在用--http-host参数强制设置http头部的HOST字段值为后端实际的
 域名和端口即可轻松解决.

v7.3
1.重构了SPS的SS功能,go版本ss,全网唯一一家支持最全加密方式,支持如下:
  aes-128-cfb , aes-128-ctr , aes-128-gcm , aes-192-cfb , aes-192-ctr , aes-192-gcm , aes-256-cfb ,
  aes-256-ctr , aes-256-gcm , bf-cfb , cast5-cfb , chacha20 , chacha20-ietf , chacha20-ietf-poly1305 ,
  des-cfb , rc4-md5 , rc4-md5-6 , salsa20 , xchacha20
2.修复某些情况下,--forever可能不能预期工作的问题.
3.修复在某些特殊Linux系统下启动失败的问题.
4.重构了核心服务管理代码,提升了效率.
5.优化了dns功能,支持多个上级负载均衡和高可用,上级写法和SPS的-P参数一致.
6.修复了--forever和--deamon存在的资源清理隐患问题.
7.优化了更新,提升了稳定性.
8.http认证某些特殊情况下不能正常工作的问题.
9.优化了所有服务的资源释放,避免某些情况下导致异常的问题.
10.SDK得到大量优化,不再会出现并发启动和停止在某些情况下导致异常的问题.

v7.2
1.sps功能增加了不再强制指定一个上级,当上级为空,sps本身即可完成完整的代理功能.
 如果指定了上级那么就和之前一样使用上级连接目标,
2.修复了dns,加密传输-z和-Z参数不能正常工作的问题.
3.优化了启动速度,提升了稳定性.
4.取消发布几乎没人使用的平台上的二进制.

v7.1
1.socks/http(s)/sps代理,负载均衡里面的权重标志符号由`@`变更为`#`.
2.sps支持同时使用不同类型的上级,比如你可以用sps同时使用socks,http,ss三种类型的上级,
 详细情况请看手册`设置单独认证信息`和`设置单独认证信息的协议类型`
3.sps代理,上级设置单独认证信息里面的用户名和密码不再使用base64编码,变更为标准的URLEncode编码.
4.修复了所有服务在某些情况下不能正常停止服务释放资源的问题.
5.重构了部分代码,运行更加高效.
6.socks/http(s)/sps代理增加了域名黑名单支持,用--stop参数指定一个域名列表文件,
 那么当用户连接文件里面这些域名的时候连接就会被断开.域名文件内容格式参见手册:"域名黑名单".
7.针对windows增加了隐藏界面的启动脚本,bootstrap.bat里面可以设置参数,start.vbs是启动脚本.
8.增加了一个广告域名列表文件40000+个域名,已经打包在发布的版本压缩包内.

v7.0
1.修复了socks代理,上级为ssh的时候,不能正常工作的问题.

v6.9
1.修复了sps的start潜在的crash问题.
2.sps代理增加了--parent-tls-single参数用来支持单向tls上级。
3.sps代理增加了对单个上级认证信息的支持,如果没有单独设置,就使用全局-A设置.
  现在上级格式: -P YTpi#*******:33080@1
  说明:
  YTpi 是经过base64编码的认证信息,比如是http(s)/socks原始认证信息a:b,用户是a密码是b,base64编码之后是:YTpi
     如果是ss,那么a就是加密方法,b是密码,比如:aes-192-cfb:your_pass,base64编码之后是:YWVzLTE5Mi1jZmI6eW91cl9wYXNz
  # 是间隔符号,如果有认证信息,必须有#,没有认证信息可以省略#.
  *******:33080 是上级地址
  @1 是设置权重,可以参考手册权重部分.
4.修复了socks5代理错误处理超时的问题.
5.修复了http(s)代理错误处理-Z的问题.

v6.8
1.HTTP(S)\SOCKS5代理,API认证功能,发送给认证接口的参数增加了本地IP,local_ip字段,
 代表用户访问的是本地服务器的哪个IP.
2.fix #194 , fix #134 , 代理更稳定.
3.增加了一波英文文档.

v6.6
1.优化了limitconn的关闭逻辑,释放更多资源.
2.http(s)\socks代理增加了--intelligent,智能模式设置,可以是intelligent|direct|parent三者之一,
默认是:intelligent.每个值的含义如下.
--intelligent=direct,不在blocked里面的目标都直连. 
--intelligent=parent,不在direct里面的目标都走上级. 
--intelligent=intelligent,blocked和direct里面都没有的目标,智能判断是否使用上级访问目标. 

v6.5
1.修复了合并企业版遗留的一些bug. 

v6.4
1.http(s)代理增加了--jumper参数,可以穿透外部代理连接上级.
2.优化了socks5代理UDP功能可能存在的内存占用过多问题.
3.优化了jumper,避免某些情况下不能正确返回错误的问题.
4.sps代理增加了--jumper参数,可以穿透外部代理连接上级.
5.修复了--debug不能正常工作的问题.

v6.3
1.fixed #156
2.修复DNS代理,没有定时保存缓存结果到文件.重启会降低查询速度.


v6.2
1.修复encrypt.Conn释放内存,导致的潜在panic问题.
2.修复了basic认证,处理认证文件没有正确处理注释的bug.
3.修正了ssh中转手册参数-A调整为-D.

v6.1
1.黑白名单支持设置顶级域了,比如:com,匹配所有的.com域名
2.优化TCPS内存释放.
3.优化了域名检查.
4.内网穿透增加了TCPS和TOU协议,
  TCPS提供了多种自定义加密TCP方式传输.
  TOU提供了TCP over UDP,多种自定义加密UDP方式传输TCP数据.
5.优化了DST,防止意外crash.
6.修复了mapx的Keys()方法的bug导致内网穿透bridge不稳定的问题.
7.修复了部分服务不能绑定IPv6地址的bug.

v6.0 企业版开源啦
本次更新主要是把企业版开源,把企业版代码合并到现在的开源goproxy当中,继续遵循GPLv3,免费开源,
之所以直接跳过5.x,用6.0版本号是为了与现有开源版本做一个明显的区分,下面功能主要来自企业版.
企业版代码结构更合理,核心与开源版本有很大区别,与此同时企业版有一个core开发库,基于此库可以
几行代码实现自己高度定制化的各种网络安全传输服务器和客户端和代理服务器与客户端.与此同时企
业版独创了TCPS协议,处于应用层和TCP层之间,可以为应用提供透明化的安全传输功能,另外还对dst协
议进行了一些改造,集成到goproxy中,实现了tcp over udp功能,那么除了kcp之外现在还可以选择dst
作为底层的tcp over udp的传输.下一步加入插件机制,定制功能可以使用插件方式开发了，热插拔，
不需要修改goproxy二进制，可以插件so或者dylib注入.

1.预编译的二进制增加了armv8支持.
2.预编译的mipsle和mips二进制增加了softfloat支持.
3.优化连接HTTP(s)上级代理的CONNECT指令,附带更多的信息.
4.重构了内网穿透的UDP功能,性能大幅度提升,可以愉快的与异地基友玩依赖UDP的局域网游戏了.
5.重构了UDP端口映射,性能大幅度提升.
6.HTTP(S)\SOCKS5\SPS代理支持上级负载均衡,可以同时指定多个上级.
7.SPS支持HTTP(S)\SOCKS5\SS协议相互转换.
8.HTTP(S)\SOCKS5\SPS代理支持限速.
9.HTTP(S)\SOCKS5代理支持指定出口IP.
10.SOCKS5代理支持级联认证.
11.修复了tclient可能意外退出的bug.
12.优化了错误捕获,防止意外crash.
13.优化了停止服务,释放内存.

v5.4
1.优化了获取本地IP信息导致CPU过高的问题.
2.所有服务都增加了--nolog参数,可以关闭日志输出,节省CPU.
3.优化sdk,支持并发启动/关闭操作.
4.修复了多连接版本的内网穿透,tserver连接不能正确释放的bug.
5.内网穿透增加了client/tclient和server/tserver使用代理连接bridge/tbridge的功能,详细内容参考手册.
6.TCP端口映射(TCP代理)增加了使用代理连接上级的功能,详细内容参考手册.

v5.3
1.优化了socks_client握手端口判断,避免了sstap测试UDP失败的问题.

v5.2
1.修复了HTTP(S)\SPS反向代理无法正常工作的问题.
2.优化了智能判断,减少不必要的DNS解析.
3.重构了SOCKS和SPS的UDP功能,基于UDP的游戏加速嗖嗖的.

v5.1
1.优化了kcp默认mtu配置,调整为450.
2.优化了HTTP(S)\SOCKS5代理智能判断，更加精确。
3.fix #97 , 修复了RemoveProxyHeaders方法忽略了第一行的bug。
4.修复了-g参数长格式没有连接符号的bug.
5.重构了证书生成功能,不再有任何外部依赖,任何平台都可以独立生成证书.

v5.0
1.修复了SPS多端口无效的bug.
2.增加了DNS代理功能，提供安全无污染的DNS解析.

v4.9
1.修复了HTTP Basic代理返回不合适的头部,导致浏览器不会弹框,个别代理插件无法认证的问题.
2.内网穿透切换smux到yamux.
3.优化了HTTP(S)\SOCKS5代理--always的处理逻辑.

v4.8
1.优化了SPS连接HTTP上级的指令,避免了某些代理不响应的问题.
2.SPS功能增加了参数:
  --disable-http:禁用http(s)代理
  --disable-socks:禁用socks代理
  默认都是false(开启).
3.重构了部分代码的日志部分,保证了日志按着预期输出.
4.修复了sps\http代理初始化服务的时机不正确,导致nil异常的bug.
5.优化了sps日志输出.
6.--debug参数增加了Profiling功能,可以保存cpu,内存等多种调试数据到文件.
7.优化了服务注册,避免了不必要的内存开销.
8.增加了Dockerfile和docker安装手册.
9.优化了ioCopy避免了内存泄漏,大大提升了内存占用的稳定性.


v4.7
1.增加了基于gomobile的sdk,对android/ios/windows/linux/mac提供SDK支持.
2.优化了bridge的日志,增加了client和server的掉线日志.
3.优化了sps读取http(s)代理响应的缓冲大小,同时优化了CONNECT请求,
 避免了某些代理服务器返回过多数据导致不能正常通讯的问题.
4.去除了鸡肋连接池功能.
5.优化了所有服务代码,方便对sdk提供支持.
6.增加了SDK手册.
7.增加了GUI客户端(windows/web/android/ios)介绍主页.
8.SPS\HTTP(s)\Socks代理增加了自定义加密传输,只需要通过参数-z和-Z设置一个密码即可.
9.SPS\HTTP(s)\Socks代理增加了压缩传输,只需要通过参数-m和-M设置即可.
10.手册增加了SPS\HTTP(s)\Socks自定义加密的使用示例.
11.手册增加了SPS\HTTP(s)\Socks压缩传输的使用示例.
12.优化了多链接版本的内网穿透,融合了多链接和smux的优点,即能够拥有大的吞吐量,
 同时又具备mux的心跳机制保证了链接的稳定性.
13.手册增加了大量配图.
14.优化了socks代理udp上级的设置逻辑,智能判断parent上级填充udp parent.
15.优化了项目文件夹结构,使用源码可以直接go get.

v4.6
1.sps,http(s),socks5,内网穿透都做了大量的超时优化处理,更加稳定.
2.sps增加了强大的树形级联认证支持,可以轻松构建你的认证代理网络.
3.手册增加了6.6对sps认证功能的介绍.


v4.5
1.优化了mux内网穿透连接管理逻辑,增强了稳定性.  
2.mux内网穿透增加了tcp和kcp协议支持,之前是tls,现在支持三种协议tcp,tls,kcp.  
3.keygen参数增加了用法: proxy keygen usage.  
4.http(s)/socks5代理,tls增加了自签名证书支持.  
5.建议升级.   
v4.4
1.增加了协议转换sps功能，代理协议转换使用的是sps子命令(socks+https的缩写)，
sps本身不提供代理功能，只是接受代理请求"转换并转发"给已经存在的http(s)代理
或者socks5代理；sps可以把已经存在的http(s)代理或者socks5代理转换为一个端口
同时支持http(s)和socks5代理，而且http(s)代理支持正向代理和反向代理(SNI)，转
换后的SOCKS5代理不支持UDP功能；另外对于已经存在的http(s)代理或者socks5代理，
支持tls、tcp、kcp三种模式，支持链式连接，也就是可以多个sps结点层级连接构建
加密通道。
2.增加了对KCP传输参数的配置，多达17个参数可以自由的配置对kcp传输效率调优。
3.内网穿透功能，server和client增加了--session-count参数，可以设置server每个
监听端口到bridge打开的session数量，可以设置client到bridge打开的session数量，
之前都是1个，现在性能提升N倍，N就是你自己设置的--session-count，这个参数很大
程度上解决了多路复用的拥塞问题，v4.4开始默认10个。

v4.3
1.优化了参数keygen生成证书逻辑，避免证书出现特征。
2.http(s)和socks代理增加了--dns-address和--dns-ttl参数。
 用于自己指定proxy访问域名的时候使用的dns（--dns-address）以及解析结果缓存时间（--dns-ttl）秒数，
 避免系统dns对proxy的干扰，另外缓存功能还能减少dns解析时间提高访问速度。
3.优化了http代理的basic认证逻辑。
提示：
v4.3生成的证书不适用于v4.2及以下版本。

v4.2
1.优化了内网穿透,避免了client意外下线,导致链接信息残留的问题.
2.http代理增加了SNI支持,现在http(s)代理模式支持反向代理,支持http(s)透明代理.
3.增加了英文手册.  

v4.1
1.优化了http(s),socks5代理中的域名智能判断,如果是内网IP,直接走本地网络,提升浏览体验,
 同时优化了检查机制,判断更快.
2.http代理basic认证增加了对https协议的支持,现在basic认证可以控制所有http(s)流量了.
3.项目代码增加了依赖类库vendor目录,clone下来就能go build,再也不用担心go get依赖类库
 失败导致不能编译了.

v4.0
1.内网穿透三端重构了一个multiplexing版本，使用github.com/xtaci/smux实现了tcp链接的多路复用，
  鼎鼎大名的kcp-go底层就是使用的这个库，基于kcp-go的双边加速工具kcptun的广泛使用已经很好
  的验证来该库的强大与稳定。multiplexing版的内网穿透对应的子命令分别是server，client，bridge
  使用方式和参数与之前的子命令tserver，tclient，tserver完全一样，另外server，client增加了
  压缩传输参数--c，使用压缩传输速度更快。

v3.9
1.增加了守护运行参数--forever,比如: proxy http --forever ,
  proxy会fork子进程,然后监控子进程,如果子进程异常退出,5秒后重启子进程.
  该参数配合后台运行参数--daemon和日志参数--log,可以保障proxy一直在后台执行不会因为意外退出,
  而且可以通过日志文件看到proxy的输出日志内容.
  比如: proxy http -p ":9090" --forever --log proxy.log --daemon

v3.8
1.增加了日志输出到文件--log参数,比如: --log proxy.log,日志就会输出到proxy.log方便排除问题.

v3.7
1.修复了socks代理不能正常和上级代理通讯的问题.


v3.6
1.http(s),socks代理,集成了外部HTTP API认证,可以通过外部API对用户名和密码进行认证.
2.手册http(s),socks代理认证部分增加了集成外部HTTP API认证的使用说明.

v3.5
1.优化了kcp参数,速度有所提升.
2.修复了socks无法正常工作的问题.
3.修正了文档中的一些描述.
4.tcp代理增加了kcp协议传输数据.
5.优化了死循环检查,增加了添加本地IP参数,当VPS在nat设备后面,
  vps上网卡IP都是内网IP,这个时候可以通过-g参数添加vps的外网ip防止死循环.
6.增加了--daemon参数,可以后台运行程序哟.  

v3.4
1.socks5代理新增了用户名密码验证支持.
2.socks5,http(s)代理增加了kcp传输协议支持.
3.优化了内网穿透的心跳机制.

v3.3
1.修复了socks代理模式对证书文件的判断逻辑.
2.增强了http代理,socks代理的ssh中转模式的稳定性.
3.socks代理tls,tcp模式新增了CMD_ASSOCIATE(udp)支持.socks代理ssh模式不支持udp.
4.修复了http代理某些情况下会崩溃的bug.

v3.2
1.内网穿透功能server端-r参数增加了协议和key设置.
2.手册增加了对-r参数的详细说明.
3.修复了普通模式也检查证书文件的bug.
4.增加了Socks5支持,目前只支持TCP协议,不支持UDP协议.
5.Socks5上级代理支持ssh中转,linux服务器不需要任何服务端,本地一个proxy即可开心上网.
6.http(s)代理增加了ssh中转支持,linux服务器不需要任何服务端,本地一个proxy即可开心上网.

v3.1
1.优化了内网穿透功能,bridge,client和server只需要启动一个即可。  
  server端启动的时候可以指定client端要暴露的一个或者多个端口。  
2.修复了重复解析命令行参数的问题。  
3.手册增加了微信接口本地开发的示例。  
4.增加了配置文件使用说明.  

v3.0
1.此次更新不兼容2.x版本,重构了全部代码,架构更合理,利于功能模块的增加与维护。
2.增加了代理死循环检查,增强了安全性。
3.增加了反向代理模式(即:内网穿透),支持TCP和UDP两种协议,可以把任何局域网的机器A所在网络的任何端。
  暴露到任何局域网的机器B的本地端口或暴露到任何公网VPS上。
4.正向代理增加了UDP模式支持。


v2.2
1.增加了强制使用上级代理参数always.可以使所有流量都走上级代理。
2.增加了定时检查网络是否正常,可以在本地网络不稳定的时候修复连接池状态,提升代理访问体验。
3.http代理增加了对ipv6地址的支持。

v2.1
1.增加了http basic验证功能,可以对http代理协议设置basic验证,用户名和密码支持来自文件或者命令行。
2.优化了域名检查方法，避免空连接的出现。
3.修复了连接上级代理超时参数传递错误导致超时过大的问题。
4.增加了连接池状态监测,如果上级代理或者网络出现问题,会及时重新初始化连接池,防止大量无效连接,降低浏览体验。
5.增加了对系统kill信号的捕获,可以在收到系统kill信号之后执行清理释放连接的操作.避免出现大量CLOSE_WAIT。

v2.0
1.增加了连接池功能,大幅提高了通过上级代理访问的速度。
2.HTTP代理模式,优化了请求URL的获取逻辑,可以支持:http,https,websocke。
3.增加了TCP代理模式,支持是否加密通讯。
4.优化了链接关闭逻辑,避免出现大量CLOSE_WAIT。
5.增加了黑白名单机制,更自由快速的访问。
6.优化了网站Block机制检测,判断更准确。

v1.0
1.始发版本,可以代理http,https。
