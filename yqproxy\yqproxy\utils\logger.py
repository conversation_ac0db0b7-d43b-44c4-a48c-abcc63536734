"""
统一日志管理器

提供集中化的日志配置和管理功能，支持：
- 多级别日志记录
- 控制台和文件输出
- 日志轮转
- 性能监控
- 错误跟踪
"""

import sys
from typing import Any, Optional, Dict
from pathlib import Path

from loguru import logger


# 默认配置
_DEFAULT_LOG_PATH = "logs/yqproxy.log"
_DEFAULT_LOG_LEVEL = "INFO"
_DEFAULT_LOG_FORMAT = (
    "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | "
    "<level>{level: <8}</level> | "
    "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
    "<level>{message}</level>"
)
_DEFAULT_ROTATION = "00:00"  # 按时间轮转（每天午夜）
_DEFAULT_RETENTION = "30 days"  # 保留时间


class LoggerManager:
    """
    日志管理器类
    
    负责管理所有日志配置和实例
    """
    
    def __init__(self):
        """初始化日志管理器"""
        self.initialized = False
        self.log_path: Optional[str] = None
        self.log_level: str = _DEFAULT_LOG_LEVEL
        self.log_enabled: bool = True
        self.console_enabled: bool = True
        self.file_enabled: bool = True
        self.handlers: Dict[str, int] = {}  # 存储处理器ID
        
    def setup(
        self,
        log_enabled: bool = True,
        log_path: Optional[str] = None,
        log_level: str = None,
        log_rotation: str = None,
        log_retention: str = None,
        console_enabled: bool = True,
        file_enabled: bool = True,
        custom_format: Optional[str] = None
    ) -> None:
        """
        配置全局日志器
        
        Args:
            log_enabled: 是否启用日志记录
            log_path: 日志文件路径
            log_level: 日志级别 (DEBUG/INFO/WARNING/ERROR/CRITICAL)
            log_rotation: 轮转策略 (如 "100 MB", "1 day", "00:00")
            log_retention: 保留策略 (如 "30 days", "10 files")
            console_enabled: 是否输出到控制台
            file_enabled: 是否输出到文件
            custom_format: 自定义日志格式
        """
        # 如果已初始化，先清理现有配置
        if self.initialized:
            self.cleanup()
        
        self.log_enabled = log_enabled
        self.console_enabled = console_enabled
        self.file_enabled = file_enabled
        
        if not log_enabled:
            # 禁用所有日志
            logger.disable("yqproxy")
            self.initialized = True
            return
            
        # 启用日志
        logger.enable("yqproxy")
        
        # 设置日志级别
        self.log_level = log_level or _DEFAULT_LOG_LEVEL
        
        # 设置日志格式
        log_format = custom_format or _DEFAULT_LOG_FORMAT
        
        # 设置轮转和保留策略
        rotation = log_rotation or _DEFAULT_ROTATION
        retention = log_retention or _DEFAULT_RETENTION
        
        # 移除默认处理器
        logger.remove()
        
        # 添加控制台处理器
        if console_enabled:
            handler_id = logger.add(
                sys.stderr,
                format=log_format,
                level=self.log_level,
                colorize=True,
                backtrace=True,
                diagnose=True
            )
            self.handlers['console'] = handler_id
        
        # 添加文件处理器
        if file_enabled and log_path:
            self.log_path = log_path
            
            # 确保日志目录存在
            log_dir = Path(log_path).parent
            log_dir.mkdir(parents=True, exist_ok=True)
            
            handler_id = logger.add(
                log_path,
                format=log_format,
                level=self.log_level,
                rotation=rotation,
                retention=retention,
                compression="gz",  # 压缩旧日志
                encoding="utf-8",
                backtrace=True,
                diagnose=True,
                enqueue=True  # 异步写入，避免阻塞
            )
            self.handlers['file'] = handler_id
            
        self.initialized = True
        
    def cleanup(self) -> None:
        """清理日志配置"""
        for handler_id in self.handlers.values():
            try:
                logger.remove(handler_id)
            except ValueError:
                pass  # 处理器可能已被删除
        self.handlers.clear()
        self.initialized = False
        
    def get_logger(self, name: str) -> Any:
        """
        获取指定名称的日志器
        
        Args:
            name: 日志器名称，通常使用 __name__
            
        Returns:
            配置好的日志器实例
        """
        return logger.bind(name=name)
        
    def set_level(self, level: str) -> None:
        """
        动态设置日志级别
        
        Args:
            level: 新的日志级别
        """
        self.log_level = level.upper()
        
        # 重新配置处理器的级别
        for key, handler_id in self.handlers.items():
            logger.remove(handler_id)
            
            if key == 'console':
                new_id = logger.add(
                    sys.stderr,
                    format=_DEFAULT_LOG_FORMAT,
                    level=self.log_level,
                    colorize=True,
                    backtrace=True,
                    diagnose=True
                )
            elif key == 'file' and self.log_path:
                new_id = logger.add(
                    self.log_path,
                    format=_DEFAULT_LOG_FORMAT,
                    level=self.log_level,
                    rotation=_DEFAULT_ROTATION,
                    retention=_DEFAULT_RETENTION,
                    compression="gz",
                    encoding="utf-8",
                    backtrace=True,
                    diagnose=True,
                    enqueue=True
                )
            else:
                continue
                
            self.handlers[key] = new_id


# 创建全局日志管理器实例
_logger_manager = LoggerManager()


def setup_logger(
    log_enabled: bool = True,
    log_path: Optional[str] = None,
    log_level: str = None,
    log_rotation: str = None,
    log_retention: str = None,
    console_enabled: bool = True,
    file_enabled: bool = True,
    custom_format: Optional[str] = None
) -> None:
    """
    配置全局日志器（便捷函数）
    
    Args:
        log_enabled: 是否启用日志记录
        log_path: 日志文件路径
        log_level: 日志级别
        log_rotation: 轮转策略
        log_retention: 保留策略
        console_enabled: 是否输出到控制台
        file_enabled: 是否输出到文件
        custom_format: 自定义日志格式
    """
    _logger_manager.setup(
        log_enabled=log_enabled,
        log_path=log_path,
        log_level=log_level,
        log_rotation=log_rotation,
        log_retention=log_retention,
        console_enabled=console_enabled,
        file_enabled=file_enabled,
        custom_format=custom_format
    )


def get_logger(name: str) -> Any:
    """
    获取指定名称的日志器
    
    Args:
        name: 日志器名称，通常使用 __name__
        
    Returns:
        配置好的日志器实例
    """
    return _logger_manager.get_logger(name)


def init_from_env() -> None:
    """从环境变量或配置文件初始化日志配置"""
    try:
        from yqproxy.settings import (
            LOG_ENABLED,
            LOG_PATH,
            LOG_LEVEL,
            LOG_ROTATION,
            LOG_RETENTION,
            LOG_CONSOLE,
            DEBUG_MODE
        )
        
        # 根据配置决定是否启用控制台输出
        console_enabled = LOG_CONSOLE
        
        setup_logger(
            log_enabled=LOG_ENABLED,
            log_path=LOG_PATH,
            log_level=LOG_LEVEL,
            log_rotation=LOG_ROTATION,
            log_retention=LOG_RETENTION,
            console_enabled=console_enabled,
            file_enabled=True
        )
        
    except ImportError:
        # 如果配置文件不存在，使用默认配置
        setup_logger(
            log_enabled=True,
            log_path=_DEFAULT_LOG_PATH,
            log_level=_DEFAULT_LOG_LEVEL,
            log_rotation=_DEFAULT_ROTATION,
            log_retention=_DEFAULT_RETENTION,
            console_enabled=True,
            file_enabled=True
        )


# 导出 loguru 的 logger 实例，用于向后兼容
__all__ = [
    'logger',
    'setup_logger',
    'get_logger',
    'init_from_env'
]


# 默认初始化（从环境变量加载配置）
init_from_env()