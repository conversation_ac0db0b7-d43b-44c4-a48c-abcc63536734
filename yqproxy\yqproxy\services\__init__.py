"""
业务服务层
依赖models和storage层，提供纯净的业务逻辑服务
"""

# 协议检测服务（纯函数，无依赖）
from yqproxy.services.protocol_detector import ProtocolDetector

# 代理测试服务（只依赖models）
from yqproxy.services.proxy_tester import (
    ProxyTester,
    TestResult,
    TestStats
)
from yqproxy.config import TestConfig, get_test_config

# 代理管理服务（依赖models和storage）
from yqproxy.services.proxy_manager import ProxyManager

# 代理验证服务
from yqproxy.services.validator import ProxyValidator

# 代理解析服务
from yqproxy.services.parser import ProxyParser

# 代理统计服务
from yqproxy.services.statistics import ProxyStatistics

__all__ = [
    # 协议检测
    'ProtocolDetector',
    
    # 代理测试
    'ProxyTester',
    'TestConfig',
    'get_test_config',
    'TestResult',
    'TestStats',
    
    # 代理管理
    'ProxyManager',
    
    # 代理验证
    'ProxyValidator',
    
    # 代理解析
    'ProxyParser',
    
    # 代理统计
    'ProxyStatistics',
]


# 便利函数：创建完整配置的services
async def create_integrated_services(redis_client=None,
                                    test_config=None,
                                    key_prefix="proxies"):
    """
    创建完整集成的services
    自动配置依赖注入，避免循环依赖
    
    Args:
        redis_client: Redis客户端，None时自动创建
        test_config: 测试配置，None时使用默认配置
        key_prefix: Redis键前缀，默认为"proxies"
        
    Returns:
        (protocol_detector, proxy_tester, proxy_manager) 元组
        
    Note:
        proxy_manager.validator 包含正确配置的 ProxyValidator 实例
        ProxyValidator 已正确注入 ProxyTester 和 ProxyPool 依赖
    """
    from yqproxy.storage import RedisClient
    from yqproxy.utils.logger import get_logger
    
    logger = get_logger(__name__)
    
    # 创建Redis客户端（如果未提供）
    if redis_client is None:
        redis_client = RedisClient()
        logger.debug("创建默认Redis客户端")
    
    # 创建协议检测器（纯函数服务）
    protocol_detector = ProtocolDetector()
    logger.debug("创建协议检测器")
    
    # 创建代理测试器
    proxy_tester = ProxyTester(test_config)
    logger.debug(f"创建代理测试器，配置: {test_config}")
    
    # 创建代理管理器并注入依赖
    proxy_manager = ProxyManager(
        redis_client=redis_client,
        key_prefix=key_prefix,
        protocol_detector=protocol_detector,
        proxy_tester=proxy_tester
    )
    
    # 验证依赖注入是否正确
    if proxy_manager.validator.proxy_tester is None:
        logger.error("ProxyValidator未正确获得ProxyTester依赖")
        raise RuntimeError("服务依赖注入失败：ProxyValidator缺少ProxyTester")
    
    if proxy_manager.validator.proxy_pool is None:
        logger.error("ProxyValidator未正确获得ProxyPool依赖")
        raise RuntimeError("服务依赖注入失败：ProxyValidator缺少ProxyPool")
    
    logger.info(f"集成服务创建完成，键前缀: {key_prefix}")
    
    return protocol_detector, proxy_tester, proxy_manager