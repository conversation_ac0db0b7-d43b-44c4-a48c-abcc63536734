"""
测试配置统一管理
所有测试相关配置的统一入口
"""

import os
from dataclasses import dataclass
from typing import List, Optional
from environs import Env

# 初始化环境变量读取器
env = Env()


@dataclass
class TestConfig:
    """
    统一的测试配置类
    所有测试相关代码都应该使用这个配置类
    """
    # 测试URL配置
    test_url_mode: str = 'common'  # 'common' 或 'eastmoney'
    test_url: str = 'https://httpbin.org/ip'
    
    # 超时配置
    timeout: int = 10
    
    # 有效状态码
    valid_status_codes: List[int] = None
    
    # 批处理配置
    batch_size: int = 50
    max_concurrent: int = 20
    
    # 评分配置开关
    dont_set_max_score: bool = False
    
    def __post_init__(self):
        """初始化后处理"""
        if self.valid_status_codes is None:
            self.valid_status_codes = [200, 302]
    
    @classmethod
    def from_env(cls) -> 'TestConfig':
        """
        从环境变量创建配置
        统一的配置读取入口
        """
        return cls(
            test_url_mode=env.str('TEST_URL_MODE', 'common'),
            test_url=env.str('TEST_URL', 'https://httpbin.org/ip'),
            timeout=env.int('TEST_TIMEOUT', 10),
            valid_status_codes=[int(x) for x in env.str('TEST_VALID_STATUS', '200,302').split(',')],
            batch_size=env.int('TESTER_BATCH_SIZE', 50),
            max_concurrent=env.int('TESTER_MAX_CONCURRENT', 20),
            dont_set_max_score=env.bool('TEST_DONT_SET_MAX_SCORE', False)
        )
    
    @classmethod
    def from_settings(cls, **kwargs) -> 'TestConfig':
        """
        从settings模块创建配置
        用于向后兼容
        """
        # 导入settings中的配置
        try:
            from yqproxy.settings import (
                TEST_URL_MODE, TEST_URL, TEST_TIMEOUT, TEST_VALID_STATUS,
                TESTER_BATCH_SIZE, TESTER_MAX_CONCURRENT, TEST_DONT_SET_MAX_SCORE
            )
            
            return cls(
                test_url_mode=kwargs.get('test_url_mode', TEST_URL_MODE),
                test_url=kwargs.get('test_url', TEST_URL),
                timeout=kwargs.get('timeout', TEST_TIMEOUT),
                valid_status_codes=kwargs.get('valid_status_codes', TEST_VALID_STATUS),
                batch_size=kwargs.get('batch_size', TESTER_BATCH_SIZE),
                max_concurrent=kwargs.get('max_concurrent', TESTER_MAX_CONCURRENT),
                dont_set_max_score=kwargs.get('dont_set_max_score', TEST_DONT_SET_MAX_SCORE)
            )
        except ImportError:
            # 如果无法导入settings，使用默认值
            return cls(**kwargs)
    
    def to_dict(self) -> dict:
        """转换为字典格式"""
        return {
            'test_url_mode': self.test_url_mode,
            'test_url': self.test_url,
            'timeout': self.timeout,
            'valid_status_codes': self.valid_status_codes,
            'batch_size': self.batch_size,
            'max_concurrent': self.max_concurrent,
            'dont_set_max_score': self.dont_set_max_score
        }
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"TestConfig(mode={self.test_url_mode}, url={self.test_url}, timeout={self.timeout}s)"


# 全局默认配置实例
DEFAULT_TEST_CONFIG = None


def get_test_config() -> TestConfig:
    """
    获取全局测试配置实例
    单例模式，确保配置一致性
    """
    global DEFAULT_TEST_CONFIG
    if DEFAULT_TEST_CONFIG is None:
        DEFAULT_TEST_CONFIG = TestConfig.from_env()
    return DEFAULT_TEST_CONFIG


def reset_test_config():
    """
    重置全局配置（主要用于测试）
    """
    global DEFAULT_TEST_CONFIG
    DEFAULT_TEST_CONFIG = None