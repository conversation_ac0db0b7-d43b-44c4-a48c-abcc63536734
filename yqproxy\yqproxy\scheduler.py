#!/usr/bin/env python3
"""
YQProxy 调度器
基于workers层重构的多进程调度器，支持配置化的工作进程管理
"""

import asyncio
import multiprocessing
import time
import os
import sys
import signal
from typing import Optional, Dict, Any

from yqproxy.utils.logger import get_logger
from yqproxy.workers.getter import ProxyGetter
from yqproxy.workers.server import app
from yqproxy.workers.tester import ProxyTesterWorker
# 从settings导入配置
from yqproxy.settings import (
    ENABLE_GETTER, ENABLE_TESTER, ENABLE_SERVER,
    SCHEDULE_GETTER_INTERVAL, SCHEDULE_TESTER_INTERVAL,
    DEBUG_MODE, API_HOST, API_PORT,
    APP_PROD_METHOD
)


# 检测系统类型
IS_WINDOWS = os.name == 'nt'

if IS_WINDOWS:
    multiprocessing.freeze_support()

# 全局进程变量
tester_process, getter_process, server_process = None, None, None


# 定义模块级的进程函数，避免序列化问题
def run_tester_process(cycle: Optional[int] = None):
    """
    运行代理测试器进程
    
    Args:
        cycle: 测试间隔（秒），如果不提供则使用配置
    """
    # 在子进程中重新获取logger
    from yqproxy.utils.logger import get_logger
    logger = get_logger(__name__)
    
    if not ENABLE_TESTER:
        logger.info('代理测试器未启用，退出')
        return
    
    cycle = cycle or SCHEDULE_TESTER_INTERVAL
    logger.info(f'启动代理测试器，间隔: {cycle}秒')
    
    # 设置进程信号处理（仅在非Windows系统上）
    if not IS_WINDOWS:
        def signal_handler(signum, frame):
            logger.info(f"测试器进程收到信号 {signum}，正在退出...")
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # 创建测试器实例
        tester = ProxyTesterWorker()
        
        # 运行循环
        loop = 0
        while True:
            logger.debug(f'测试器循环 {loop} 开始...')
            
            # 运行异步测试任务
            asyncio.run(tester.run_once())
            
            loop += 1
            time.sleep(cycle)
            
    except KeyboardInterrupt:
        logger.info("测试器进程被用户中断")
    except Exception as e:
        logger.error(f"测试器进程运行错误: {e}")
    finally:
        logger.info("测试器进程退出")


def run_getter_process(cycle: Optional[int] = None):
    """
    运行代理获取器进程
    
    Args:
        cycle: 获取间隔（秒），如果不提供则使用配置
    """
    # 在子进程中重新获取logger
    from yqproxy.utils.logger import get_logger
    logger = get_logger(__name__)
    
    if not ENABLE_GETTER:
        logger.info('代理获取器未启用，退出')
        return
    
    cycle = cycle or SCHEDULE_GETTER_INTERVAL
    logger.info(f'启动代理获取器，间隔: {cycle}秒')
    
    # 设置进程信号处理（仅在非Windows系统上）
    if not IS_WINDOWS:
        def signal_handler(signum, frame):
            logger.info(f"获取器进程收到信号 {signum}，正在退出...")
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # 创建获取器实例
        getter = ProxyGetter()
        
        # 运行循环
        loop = 0
        while True:
            logger.debug(f'获取器循环 {loop} 开始...')
            
            # 运行异步获取任务
            asyncio.run(getter.run_once())
            
            loop += 1
            time.sleep(cycle)
            
    except KeyboardInterrupt:
        logger.info("获取器进程被用户中断")
    except Exception as e:
        logger.error(f"获取器进程运行错误: {e}")
    finally:
        logger.info("获取器进程退出")


def run_server_process():
    """
    运行API服务器进程
    """
    # 在子进程中重新获取logger
    from yqproxy.utils.logger import get_logger
    logger = get_logger(__name__)
    
    if not ENABLE_SERVER:
        logger.info('API服务器未启用，退出')
        return
    
    logger.info('启动API服务器...')
    
    # 设置进程信号处理（仅在非Windows系统上）
    if not IS_WINDOWS:
        def signal_handler(signum, frame):
            logger.info(f"服务器进程收到信号 {signum}，正在退出...")
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        if not DEBUG_MODE:
            # 生产模式
            _run_production_server()
        else:
            # 调试模式
            _run_uvicorn_server()
            
    except KeyboardInterrupt:
        logger.info("服务器进程被用户中断")
    except Exception as e:
        logger.error(f"服务器进程运行错误: {e}")
    finally:
        logger.info("服务器进程退出")


def _run_production_server():
    """运行生产模式服务器"""
    # 在子进程中重新获取logger
    from yqproxy.utils.logger import get_logger
    logger = get_logger(__name__)
    
    if APP_PROD_METHOD == 'gevent':
        try:
            from gevent.pywsgi import WSGIServer
            http_server = WSGIServer((API_HOST, API_PORT), app)
            logger.info(f"使用Gevent服务器在 {API_HOST}:{API_PORT} 启动")
            http_server.serve_forever()
        except ImportError as e:
            logger.error(f"Gevent不可用: {e}")
            _run_uvicorn_server()
            
    elif APP_PROD_METHOD == 'tornado':
        try:
            from tornado.wsgi import WSGIContainer
            from tornado.httpserver import HTTPServer
            from tornado.ioloop import IOLoop
            
            http_server = HTTPServer(WSGIContainer(app))
            http_server.listen(API_PORT)
            logger.info(f"使用Tornado服务器在 {API_HOST}:{API_PORT} 启动")
            IOLoop.instance().start()
        except ImportError as e:
            logger.error(f"Tornado不可用: {e}")
            _run_uvicorn_server()
            
    elif APP_PROD_METHOD == 'meinheld':
        try:
            import meinheld
            meinheld.listen((API_HOST, API_PORT))
            logger.info(f"使用Meinheld服务器在 {API_HOST}:{API_PORT} 启动")
            meinheld.run(app)
        except ImportError as e:
            logger.error(f"Meinheld不可用: {e}")
            _run_uvicorn_server()
    else:
        logger.warning(f"不支持的生产服务器方法: {APP_PROD_METHOD}")
        _run_uvicorn_server()


def _run_uvicorn_server():
    """使用Uvicorn运行FastAPI服务器"""
    # 在子进程中重新获取logger
    from yqproxy.utils.logger import get_logger
    logger = get_logger(__name__)
    
    import uvicorn
    
    logger.info(f"使用Uvicorn服务器在 {API_HOST}:{API_PORT} 启动")
    uvicorn.run(
        app,
        host=API_HOST,
        port=API_PORT,
        workers=1,  # 默认1个worker
        log_level="info" if not DEBUG_MODE else "debug"
    )


class ProxyScheduler:
    """
    代理池调度器
    管理getter、tester、server三个工作进程的生命周期
    """
    
    def __init__(self):
        """
        初始化调度器
        """
        # 初始化日志器
        self.logger = get_logger(__name__)
        
        self.processes: Dict[str, Optional[multiprocessing.Process]] = {
            'tester': None,
            'getter': None,
            'server': None
        }
        
        # 进程目标函数映射（使用模块级函数，避免序列化问题）
        self.process_targets = {
            'tester': run_tester_process,
            'getter': run_getter_process,
            'server': run_server_process
        }
        
        self.logger.info(f"调度器初始化完成")
    
    def start_process(self, process_name: str) -> bool:
        """
        启动指定进程
        
        Args:
            process_name: 进程名称 (tester/getter/server)
            
        Returns:
            bool: 是否启动成功
        """
        if process_name not in self.process_targets:
            self.logger.error(f"未知的进程名称: {process_name}")
            return False
        
        if self.processes[process_name] is not None:
            self.logger.warning(f"进程 {process_name} 已在运行")
            return False
        
        try:
            target_func = self.process_targets[process_name]
            process = multiprocessing.Process(
                target=target_func,
                name=f"YQProxy-{process_name}"
            )
            process.start()
            self.processes[process_name] = process
            
            self.logger.info(f'启动 {process_name} 进程，PID: {process.pid}')
            return True
            
        except Exception as e:
            self.logger.error(f"启动 {process_name} 进程失败: {e}")
            return False
    
    def stop_process(self, process_name: str) -> bool:
        """
        停止指定进程
        
        Args:
            process_name: 进程名称
            
        Returns:
            bool: 是否停止成功
        """
        process = self.processes.get(process_name)
        if process is None:
            self.logger.warning(f"进程 {process_name} 未运行")
            return False
        
        try:
            if process.is_alive():
                self.logger.info(f"正在终止 {process_name} 进程...")
                process.terminate()
                process.join(timeout=10)
                
                if process.is_alive():
                    self.logger.warning(f"强制杀死 {process_name} 进程")
                    process.kill()
                    process.join()
            
            self.processes[process_name] = None
            self.logger.info(f"{process_name} 进程已停止")
            return True
            
        except Exception as e:
            self.logger.error(f"停止 {process_name} 进程失败: {e}")
            return False
    
    def run(self):
        """
        运行调度器主循环
        启动所有启用的进程并等待它们完成
        """
        global tester_process, getter_process, server_process
        
        try:
            self.logger.info('正在启动代理池调度器...')
            
            # 启动各个进程
            enabled_processes = []
            
            if ENABLE_TESTER:
                if self.start_process('tester'):
                    enabled_processes.append('tester')
                    tester_process = self.processes['tester']
            
            if ENABLE_GETTER:
                if self.start_process('getter'):
                    enabled_processes.append('getter')
                    getter_process = self.processes['getter']
            
            if ENABLE_SERVER:
                if self.start_process('server'):
                    enabled_processes.append('server')
                    server_process = self.processes['server']
            
            if not enabled_processes:
                self.logger.warning("没有启用任何进程")
                return
            
            self.logger.info(f"代理池调度器启动完成，运行的进程: {', '.join(enabled_processes)}")
            
            # 等待所有进程
            for process_name in enabled_processes:
                process = self.processes[process_name]
                if process:
                    process.join()
                    
        except KeyboardInterrupt:
            self.logger.info('收到键盘中断信号，正在停止所有进程...')
            self.stop_all_processes()
            
        except Exception as e:
            self.logger.error(f"调度器运行错误: {e}")
            self.stop_all_processes()
            
        finally:
            self.cleanup()
    
    def stop_all_processes(self):
        """停止所有进程"""
        for process_name in list(self.processes.keys()):
            self.stop_process(process_name)
    
    def cleanup(self):
        """清理资源"""
        self.logger.info("正在清理调度器资源...")
        
        # 检查进程状态
        for process_name, process in self.processes.items():
            if process:
                status = "alive" if process.is_alive() else "dead"
                self.logger.info(f'{process_name} 进程状态: {status}')
        
        self.logger.info('代理池调度器已终止')
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取调度器状态
        
        Returns:
            Dict[str, Any]: 状态信息
        """
        status = {
            'config': {
                'enable_tester': ENABLE_TESTER,
                'enable_getter': ENABLE_GETTER,
                'enable_server': ENABLE_SERVER,
                'tester_interval': SCHEDULE_TESTER_INTERVAL,
                'getter_interval': SCHEDULE_GETTER_INTERVAL
            },
            'processes': {}
        }
        
        for process_name, process in self.processes.items():
            if process:
                status['processes'][process_name] = {
                    'pid': process.pid,
                    'alive': process.is_alive(),
                    'name': process.name
                }
            else:
                status['processes'][process_name] = None
        
        return status


def main():
    """主函数"""
    # Windows系统需要特殊处理
    if IS_WINDOWS:
        # 设置启动方法为spawn（Windows默认）
        multiprocessing.set_start_method('spawn', force=True)
    
    # 创建和运行调度器
    scheduler = ProxyScheduler()
    scheduler.run()


if __name__ == '__main__':
    # Windows系统支持
    if IS_WINDOWS:
        multiprocessing.freeze_support()
    main()
