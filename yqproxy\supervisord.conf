[supervisord]
nodaemon=true
logfile=/dev/null
logfile_maxbytes=0
loglevel=info

[program:tester]
command=python3 -u /app/run.py --processor tester
directory=/app
autostart=true
autorestart=true
startretries=3
startsecs=5
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
stopasgroup=true
killasgroup=true
priority=10

[program:getter]
command=python3 -u /app/run.py --processor getter
directory=/app
autostart=true
autorestart=true
startretries=3
startsecs=5
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
stopasgroup=true
killasgroup=true
priority=20

[program:server]
command=python3 -u /app/run.py --processor server
directory=/app
autostart=true
autorestart=true
startretries=3
startsecs=3
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
stopasgroup=true
killasgroup=true
priority=30