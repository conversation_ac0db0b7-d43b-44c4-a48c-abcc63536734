<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
<meta name="pinterest" content="nopin">
<meta name="viewport" content="width=device-width,minimum-scale=1,initial-scale=1">
<meta name="generator" content="Hugo 0.56.3" />



<link rel="canonical" href="https://snail007.goproxyauth.com/goproxy/usage/first/">


    <link href="//maxcdn.bootstrapcdn.com/font-awesome/4.5.0/css/font-awesome.min.css" rel="stylesheet">
    <link rel="stylesheet" href="//maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css">
    <link rel="stylesheet" href="//cdnjs.cloudflare.com/ajax/libs/highlight.js/8.4/styles/solarized_dark.min.css">
    <title>手册前言（使用必读） - GOPROXY IN ACTION</title>
    
<meta name="description" content="GOPROXY手册使用">

<meta property="og:title" content="手册前言（使用必读） - GOPROXY IN ACTION">
<meta property="og:type" content="article">
<meta property="og:url" content="https://snail007.goproxyauth.com/goproxy/usage/first/">
<meta property="og:image" content="https://snail007.goproxyauth.com/goproxy/images/default.png">
<meta property="og:site_name" content="GOPROXY IN ACTION">
<meta property="og:description" content="GOPROXY手册使用">
<meta property="og:locale" content="zh_CN">

<meta name="twitter:card" content="summary_large_image">
<meta name="twitter:site" content="GOPROXY IN ACTION">
<meta name="twitter:url" content="https://snail007.goproxyauth.com/goproxy/usage/first/">
<meta name="twitter:title" content="手册前言（使用必读） - GOPROXY IN ACTION">
<meta name="twitter:description" content="GOPROXY手册使用">
<meta name="twitter:image" content="https://snail007.goproxyauth.com/goproxy/images/default.png">


<script type="application/ld+json">
  {
    "@context": "http://schema.org",
    "@type": "NewsArticle",
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id":"https:\/\/snail007.goproxyauth.com\/goproxy\/"
    },
    "headline": "手册前言（使用必读） - GOPROXY IN ACTION",
    "image": {
      "@type": "ImageObject",
      "url": "https:\/\/snail007.goproxyauth.com\/goproxy\/images\/default.png",
      "height": 800,
      "width": 800
    },
    "datePublished": "2019-07-04T11:00:50JST",
    "dateModified": "2019-07-04T11:00:50JST",
    "author": {
      "@type": "Person",
      "name": "GOPROXY IN ACTION"
    },
    "publisher": {
      "@type": "Organization",
      "name": "GOPROXY IN ACTION",
      "logo": {
        "@type": "ImageObject",
        "url": "https:\/\/snail007.goproxyauth.com\/goproxy\/images/logo.png",
        "width": 600,
        "height": 60
      }
    },
    "description": "GOPROXY手册使用"
  }
</script>


    <link href="https://snail007.goproxyauth.com/goproxy/css/styles.css" rel="stylesheet">
    

  </head>

  <body>
    
    
    

    <header class="l-header">
      <nav class="navbar navbar-default">
        <div class="container">
          <div class="navbar-header">
            <button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#navbar" aria-expanded="false">
              <span class="sr-only">Toggle navigation</span>
              <span class="icon-bar"></span>
              <span class="icon-bar"></span>
              <span class="icon-bar"></span>
            </button>
            <a class="navbar-brand" href="https://snail007.goproxyauth.com/goproxy/">GOPROXY IN ACTION</a>
          </div>

          
          <div id="navbar" class="collapse navbar-collapse">
            
            <ul class="nav navbar-nav navbar-right">
              
              
              <li><a href="https://github.com/snail007/goproxy/releases/">下载</a></li>
              
              
              
              <li><a href="https://snail007.goproxyauth.com/goproxy/categories/goproxy%E6%89%8B%E5%86%8C/">实战</a></li>
              
              
              
              <li><a href="https://snail007.goproxyauth.com/goproxy/manual/zh/#/">手册</a></li>
              
              
              
              <li><a href="/goproxy/about/">关于</a></li>
              
              
            </ul>
            
          </div>
          

        </div>
      </nav>
    </header>

    <main>
      <div class="container">
        
<div class="row">
  <div class="col-md-8">

    <nav class="p-crumb">
      <ol class="breadcrumb">
        <li><a href="https://snail007.goproxyauth.com/goproxy/"><i class="fa fa-home" aria-hidden="true"></i></a></li>
        
        <li itemscope="" itemtype="http://data-vocabulary.org/Breadcrumb"><a href="https://snail007.goproxyauth.com/goproxy/usage/" itemprop="url"><span itemprop="title">usage</span></a></li>
        
        <li class="active">手册前言（使用必读）</li>
      </ol>
    </nav>

    <article class="single">
  <header>
    <ul class="p-facts">
      <li><i class="fa fa-calendar" aria-hidden="true"></i><time datetime="2019-07-04T11:00:50JST">Jul 4, 2019</time></li>
      <li><i class="fa fa-bookmark" aria-hidden="true"></i><a href="https://snail007.goproxyauth.com/goproxy/usage/">usage</a></li>
      
    </ul>

    <h1 class="title">手册前言（使用必读）</h1>
  </header>

  

  <div class="article-body">

<h3 id="前言">前言</h3>

<h4 id="goproxy使用必读">GOPROXY使用必读</h4>

<ol>
<li><p>手册和项目中使用了一部分的名词，它们本身的含义和在goproxy的专有含义是不一样的，所以前言中对这些名词做了解释，不然直接看具体手册章节必然会出现理解偏差，导致不能正确的使用goproxy。</p></li>

<li><p>goproxy的很多功能都使用了相同的核心技术，比如底层传输：tcps传输协议，ws协议，kcp参数，黑白名单，认证等，它们的用法是一样的。
为了手册的简洁，相同的功能的说明，就放在这里统一说明，不在每个地方重复的说明。</p></li>

<li><p>proxy功能很多，往往你想要用的只是其中一个功能，那么不要被手册其它功能内容吓到和干扰，你只需要看前言和你需要用到的功能的手册部分即可！</p></li>
</ol>

<h4 id="名词解释">名词解释</h4>

<p>本站包括项目手册，如果没有特殊说明：</p>

<ol>
<li><p><code>goproxy</code>，<code>proxy</code>，均指本项目：<a href="https://github.com/snail007/goproxy">https://github.com/snail007/goproxy</a> 。</p></li>

<li><p><code>本地</code>，均指<code>需要使用代理的应用程序</code>直接连接的<code>代理</code>，一般是你日常使用的机器，也可能是一个服务器。</p></li>

<li><p><code>代理链</code>，指多个相连的代理组成的一个代理链条，一个代理链中至少有两个上下相连的代理，链条中的每一个代理不一定都是proxy提供的，可以是第三方标准代理。</p>

<p>为了区分，链条的 <strong><em>末端</em></strong> 是入口，称为<code>本地代理</code>是最后一级；链条的 <strong><em>顶端</em></strong> 是出口，称为<code>一级</code>代理。</p>

<p>连接一级代理的是二级代理，连接二级代理的是三级级代理，以此类推。</p>

<p>由此可见，本地和一级代理是链条的两端，链条中至少两个代理，链条中间的称为n级代理，越靠近本地n就越大。</p>

<p>如果链条只有两个代理，那么二级代理和本地代理指的是同一个代理。</p></li>

<li><p><code>本级</code>，均指链条中，任意一个代理。</p></li>

<li><p><code>上级</code>，<code>父级</code>，<code>parent</code>，均指：本级代理连接的上一级代理，可以是proxy提供的代理也可以是第三方标准代理。</p></li>
</ol>

<h4 id="分层架构">分层架构</h4>

<ol>
<li><p>goproxy数据流动架构，主要分为两层。</p></li>

<li><p>上层是我们熟悉的各种标准服务：socks5代理，http(s)代理，sps代理（ss+http(s)+socks5），tcp代理，udp代理，dns代理，内网穿透。</p>

<p>也就是命令中的第一参数，代表服务，也称为功能模块，功能。比如：http，socks，sps，tcp，udp，dns等。</p></li>

<li><p>下层也就是底层，负责数据传输，传输的协议和有很多，比如：tcp，tcps，ws，tls，wss，tou，kcp，不同的协议使用的基础协议也是不一样的，</p>

<p>其中除了tou和kcp使用UDP作为基础协议之外，其它传输协议均是基于TCP。也就是说tou和kcp监听的是UDP端口，其它监听的是TCP端口。</p>

<p>传输协议在proxy的参数中体现为-T、-t，-T是连接上级使用的传输协议。-t是本地监听的端口使用的传输协议。</p>

<p>本级代理的-T和和上级代理的-t应该是一致的，不然是无法通讯的，这很好理解，对方说什么语言你用什么语言去聆听才能明白对方说些什么。</p></li>
</ol>

<h4 id="ip地址">IP地址</h4>

<p>由于代理示例用到各种IP，示例中统一用IP_X，X是机器名词代号，比如A，B，这样的形式表示一个机器的IP；你在复制命令后实际使用前，记得一定要根据自己情况替换IP_X为你自己的实际IP地址。如果示例中提到了机器A，那么默认它的IP地址就是IP_A，不再重复声明。</p>

<h4 id="高级使用">高级使用</h4>

<p>proxy命令格式是：proxy <code>功能名称</code> 参数1 参数2 参数n&hellip;，proxy功能名称和功能介绍如下：</p>

<p><code>http</code> http(s)代理</p>

<p><code>socks</code> socks5代理</p>

<p><code>sps</code> http(s)+socks5+ss综合代理，一个端口同时支持3种代理</p>

<p><code>tcp</code> tcp代理，tcp端口转发</p>

<p><code>udp</code> udp代理，udp端口转发</p>

<p><code>dns</code> dns代理，dns服务器，dns转发</p>

<p><code>bridge</code> 多路复用版内网穿透的bridge端</p>

<p><code>client</code> 多路复用版内网穿透的client端</p>

<p><code>server</code> 多路复用版内网穿透的server端</p>

<p><code>tbridge</code> 多链接版内网穿透的bridge端</p>

<p><code>tclient</code> 多链接版内网穿透的client端</p>

<p><code>tserver</code> 多链接版内网穿透的server端</p>

<p>由于每个功能参数众多，有的很少用，默认值就能很好工作，手册只是介绍了高频的参数的使用，未提及的参数可以通过 proxy help <code>功能名称</code> ，查看到功能相关所有的参数。</p>

<p>如果你想深入了解和掌握proxy每个功能的用法，那么动起手来试试每个参数吧，去体会，去感受就能深刻理解并熟练掌握它。</p>

<h4 id="kcp配置">KCP配置</h4>

<p>很多功能的底层传输协议都支持kcp，kcp参数都是以<code>--kcp</code>开头，可以通过proxy help 功能名称 ，比如：proxy help http，去查看所有的参数，包括kcp参数。</p>

<p>虽然参数很多，但是proxy针对大众情况，设置了一波默认值，如果发现默认参数不合你口味，那么你就需要自己搜索kcp每个参数的意思和用法，资料很多，本手册不再赘述。</p>

<p>当然最简单的用法就是设置一个kcp密码即可，KCP协议需要&ndash;kcp-key参数设置一个密码用于加密解密数据。</p>

<h4 id="tls证书">TLS证书</h4>

<p>为了安全很多功能都可以使用底层tls传输协议加密传输数据，tls需要一个proxy.crt证书文件和一个私钥proxy.key文件。</p>

<p>这两个文件可以通过命令：<code>proxy keygen proxy</code> 生成。</p>

<p>另外，tls证书只需要在一个proxy端生成，其它端复制过去即可，要保证所有端证书一样。</p>
</div>

  <footer class="article-footer">
    
    
    
    <section class="bordered">
      <header>
        <div class="panel-title">CATEGORIES</div>
      </header>
      <div>
        <ul class="p-terms">
          
          <li><a href="https://snail007.goproxyauth.com/goproxy/categories/goproxy%E6%89%8B%E5%86%8C/">GOPROXY手册</a></li>
          
        </ul>
      </div>
    </section>
    
    
    
    
  </footer>

</article>


    <div id="disqus_thread"></div>
<script type="application/javascript">
    var disqus_config = function () {
    
    
    
    };
    (function() {
        if (["localhost", "127.0.0.1"].indexOf(window.location.hostname) != -1) {
            document.getElementById('disqus_thread').innerHTML = 'Disqus comments not available by default when the website is previewed locally.';
            return;
        }
        var d = document, s = d.createElement('script'); s.async = true;
        s.src = '//' + "goproxy" + '.disqus.com/embed.js';
        s.setAttribute('data-timestamp', +new Date());
        (d.head || d.body).appendChild(s);
    })();
</script>
<noscript>Please enable JavaScript to view the <a href="https://disqus.com/?ref_noscript">comments powered by Disqus.</a></noscript>
<a href="https://disqus.com" class="dsq-brlink">comments powered by <span class="logo-disqus">Disqus</span></a>
  </div>

  <div class="col-md-4">
    
<aside class="l-sidebar">

  <section class="panel panel-default">
    <div class="panel-heading">
      <div class="panel-title">LATESTS</div>
    </div>
    <div class="list-group">
      
      <a href="https://snail007.goproxyauth.com/goproxy/usage/first/" class="list-group-item">手册前言（使用必读）</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/page/free_vs_commercial/" class="list-group-item">GOPROXY免费版VS商业版</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/page/faq/goproxy%E5%B8%B8%E8%A7%81%E9%97%AE%E9%A2%98%E8%A7%A3%E7%AD%94/" class="list-group-item">常见问题解答</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/posts/domain-cf/" class="list-group-item">域名解析 域名控制台&#43;极客&#43;cloudflare 的配置方法</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/posts/windows-global-proxy-using-dns/" class="list-group-item">在windows下实现基于dns的自动全局代理</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/usage/tcp/" class="list-group-item">TCP代理</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/posts/cloudflare/" class="list-group-item">Cloudflare使用简明教程</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/posts/http-nat-cdn/" class="list-group-item">内网穿透也能用CDN？？？！</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/page/free_vs_commercial_en/" class="list-group-item">GOPROXY Free Version VS Commercial Version</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/posts/http_cdn_ws/" class="list-group-item">代理之套用CDN</a>
      
    </div>
  </section>

  
  <section class="panel panel-default">
    <div class="panel-heading">
      <div class="panel-title">CATEGORY</div>
    </div>
    <div class="list-group">
      
      <a href="https://snail007.goproxyauth.com/goproxy/categories/%E9%BB%98%E8%AE%A4%E5%88%86%E7%B1%BB" class="list-group-item">默认分类</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/categories/goproxy%E6%89%8B%E5%86%8C" class="list-group-item">goproxy手册</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/categories/%E6%9E%B6%E6%9E%84%E8%A7%A3%E8%AF%B4" class="list-group-item">架构解说</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/categories/%E7%BB%86%E8%AF%B4%E5%B1%82%E7%BA%A7" class="list-group-item">细说层级</a>
      
    </div>
  </section>
  
  <section class="panel panel-default">
    <div class="panel-heading">
      <div class="panel-title">TAG</div>
    </div>
    <div class="list-group">
      
      <a href="https://snail007.goproxyauth.com/goproxy/tags/cdn" class="list-group-item">cdn</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/tags/ws" class="list-group-item">ws</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/tags/cloudflare" class="list-group-item">cloudflare</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/tags/%E5%95%86%E4%B8%9A%E7%89%88" class="list-group-item">商业版</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/tags/commercial" class="list-group-item">commercial</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/tags/domain" class="list-group-item">domain</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/tags/tcp" class="list-group-item">tcp</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/tags/%E5%85%A8%E5%B1%80%E4%BB%A3%E7%90%86" class="list-group-item">全局代理</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/tags/%E5%86%85%E7%BD%91%E7%A9%BF%E9%80%8F" class="list-group-item">内网穿透</a>
      
    </div>
  </section>
  

</aside>


  </div>
</div>

      </div>
    </main>

    <footer class="l-footer">
      <div class="container">
        <p><span class="h-logo">&copy; GOPROXY IN ACTION</span></p>
      </div>
    </footer>

    <script src="//code.jquery.com/jquery-3.1.1.min.js"></script>
    <script src="//maxcdn.bootstrapcdn.com/bootstrap/3.3.7/js/bootstrap.min.js"></script>
    <script src="//cdnjs.cloudflare.com/ajax/libs/highlight.js/8.4/highlight.min.js"></script>
    <script>hljs.initHighlightingOnLoad();</script>
    <script>
    $(function(){
      $("img[css]").each(function(){
        $(this).attr("style",$(this).attr("css"));
      });
    });
    </script>
  </body>
</html>

