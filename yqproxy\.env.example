# ============================================================================
# YQProxy 生产环境配置模板
# 基于 yqproxy/settings.py 实际支持的配置
# 使用说明：复制此文件为 .env.production 并填写实际配置值
# ============================================================================

# ============================================================================
# 🚀 应用环境配置
# ============================================================================
# 调试模式（生产环境必须设为 false）
DEBUG_MODE=false

# 生产环境WSGI容器选择 (gevent/tornado/meinheld)
APP_PROD_METHOD=gevent

# ============================================================================
# ⚙️ 核心服务启用开关
# ============================================================================
ENABLE_GETTER=true                # 启用代理获取器
ENABLE_TESTER=true                # 启用代理测试器
ENABLE_SERVER=true                # 启用API服务器

# ============================================================================
# 🔧 Redis 基础配置
# ============================================================================
# 支持两种配置前缀：PROXYPOOL_REDIS_* 或 REDIS_*
REDIS_HOST=your-redis-host.example.com
REDIS_PORT=6379
REDIS_PASSWORD=your-secure-redis-password
REDIS_DB=0

# Redis连接字符串（可选，优先级最高）
# REDIS_CONNECTION_STRING=redis://:password@host:port/db

# ============================================================================
# 🔄 Redis 性能优化配置
# ============================================================================
REDIS_PIPELINE_ENABLED=true
REDIS_PIPELINE_BATCH_SIZE=100
REDIS_PIPELINE_MAX_SIZE=500
REDIS_PIPELINE_USE_TRANSACTION=true

# ============================================================================
# 📦 代理池容量管理
# ============================================================================
PROXY_NUMBER_MAX_INVALID=1000     # 总容量上限
PROXY_NUMBER_MIN_VALID=500        # 最少有效数量

# ============================================================================
# ⏰ 调度器配置
# ============================================================================
SCHEDULE_GETTER_INTERVAL=120      # 获取器间隔(秒)
SCHEDULE_TESTER_INTERVAL=300      # 测试器间隔(秒)

# ============================================================================
# 🌐 代理获取器配置
# ============================================================================
GETTER_MAX_WORKERS=10             # 并发工作线程数

# ============================================================================
# 🧪 代理测试器配置
# ============================================================================
# 测试模式配置
TEST_URL_MODE=common              # common或eastmoney
TEST_URL=https://httpbin.org/ip
TEST_TIMEOUT=10

# 批处理和并发配置
TESTER_BATCH_SIZE=50              # 每批测试的代理数量
TESTER_MAX_CONCURRENT=20          # 批内最大并发连接数

# 验证配置
TEST_VALID_STATUS=200,302         # 接受的HTTP状态码
TEST_DONT_SET_MAX_SCORE=false     # 是否不设置最高分

# ============================================================================
# 🌐 API服务配置
# ============================================================================
API_HOST=0.0.0.0
API_PORT=5555
API_KEY=generate-strong-api-key-here  # 生产环境必须设置强密码
API_TIMEOUT=5
API_THREADED=true
API_BATCH_LIMIT=50                    # 单次批量获取代理的最大数量限制

# ============================================================================
# 📝 日志配置
# ============================================================================
LOG_ENABLED=true
LOG_DIR=logs                      # 相对于项目根目录
# LOG_PATH=/custom/path/yqproxy.log  # 可选：自定义日志文件路径
LOG_LEVEL=INFO                    # 生产环境使用 INFO 级别
LOG_ROTATION=00:00                # 每天午夜轮转
LOG_RETENTION=30 days             # 保留30天
LOG_CONSOLE=false                 # 生产环境建议关闭控制台输出

# ============================================================================
# 📋 配置说明
# ============================================================================
# 1. Redis 配置：
#    - REDIS_HOST: Redis 服务器地址，可以是内网IP、域名或容器名
#    - REDIS_PASSWORD: 强密码，建议使用随机生成的32位以上字符串
#    - 可以使用 PROXYPOOL_REDIS_* 前缀替代 REDIS_* 前缀
#
# 2. API 安全：
#    - API_KEY: 必须设置强密码，建议使用 openssl rand -hex 32 生成
#    - 留空则不启用认证（不推荐在生产环境）
#
# 3. 性能优化：
#    - TESTER_BATCH_SIZE: 根据服务器性能调整批量测试数量
#    - TESTER_MAX_CONCURRENT: 批内并发数，避免设置过高
#    - GETTER_MAX_WORKERS: 获取器并发数
#    - SCHEDULE_*_INTERVAL: 根据代理池使用情况调整调度间隔
#
# 4. 日志管理：
#    - LOG_DIR: 确保应用有写入权限
#    - LOG_RETENTION: 根据磁盘空间调整日志保留时间
#    - LOG_LEVEL: 生产环境建议使用 INFO，调试时可用 DEBUG
#
# 5. 部署建议：
#    - 使用环境变量或密钥管理服务管理敏感配置
#    - 建议使用外部 Redis 服务
#    - 定期备份 Redis 数据