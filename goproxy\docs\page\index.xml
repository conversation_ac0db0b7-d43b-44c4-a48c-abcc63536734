<?xml version="1.0" encoding="utf-8" standalone="yes" ?>
<rss version="2.0" xmlns:atom="http://www.w3.org/2005/Atom">
  <channel>
    <title>Pages on GOPROXY IN ACTION</title>
    <link>https://snail007.goproxyauth.com/goproxy/page/</link>
    <description>Recent content in Pages on GOPROXY IN ACTION</description>
    <generator>Hugo -- gohugo.io</generator>
    <language>zh-CN</language>
    <lastBuildDate>Wed, 26 Jun 2019 13:19:15 +0800</lastBuildDate>
    
	<atom:link href="https://snail007.goproxyauth.com/goproxy/page/index.xml" rel="self" type="application/rss+xml" />
    
    
    <item>
      <title>GOPROXY免费版VS商业版</title>
      <link>https://snail007.goproxyauth.com/goproxy/page/free_vs_commercial/</link>
      <pubDate>Wed, 26 Jun 2019 13:19:15 +0800</pubDate>
      
      <guid>https://snail007.goproxyauth.com/goproxy/page/free_vs_commercial/</guid>
      <description>目前，GOPROXY 和 SDK 提供免费版和商业版，功能对比如下。 免费版 商业版 TCP转发 √ √ UDP转发 √ √ HTTP(S)代理 √ √ Socks5代理 √ √</description>
    </item>
    
    <item>
      <title>常见问题解答</title>
      <link>https://snail007.goproxyauth.com/goproxy/page/faq/goproxy%E5%B8%B8%E8%A7%81%E9%97%AE%E9%A2%98%E8%A7%A3%E7%AD%94/</link>
      <pubDate>Fri, 14 Jun 2019 16:29:13 +0800</pubDate>
      
      <guid>https://snail007.goproxyauth.com/goproxy/page/faq/goproxy%E5%B8%B8%E8%A7%81%E9%97%AE%E9%A2%98%E8%A7%A3%E7%AD%94/</guid>
      <description>问：http代理支持https网站吗？ 答：goproxy的http代理同时支持http和https网站。 问：socks5代理支持UDP吗？ 答</description>
    </item>
    
    <item>
      <title>GOPROXY Free Version VS Commercial Version</title>
      <link>https://snail007.goproxyauth.com/goproxy/page/free_vs_commercial_en/</link>
      <pubDate>Wed, 26 Jun 2019 13:19:15 +0800</pubDate>
      
      <guid>https://snail007.goproxyauth.com/goproxy/page/free_vs_commercial_en/</guid>
      <description>Currently, GOPROXY and SDK are available in both free and commercial versions. Function Free |Commercial TCP forwarding √ √ UDP forwarding √ √ HTTP(S)Agent √ √ Socks5 Agent √ √ Socks5 proxy UDP support √ √ SPS Integrated Agent √ √ SPS superior certification √ √ Intranet penetration √ √ DNS proxy, anti-pollution, independent DNS service √ √ HTTP(S) proxy authentication, authentication API, load balancing × √ SOCKS5 proxy authentication,</description>
    </item>
    
    <item>
      <title>Categories</title>
      <link>https://snail007.goproxyauth.com/goproxy/page/categories/</link>
      <pubDate>Fri, 14 Jun 2019 15:29:47 +0800</pubDate>
      
      <guid>https://snail007.goproxyauth.com/goproxy/page/categories/</guid>
      <description>不用看这里，没什么内容，仅仅为了定义目录和tags。</description>
    </item>
    
    <item>
      <title>About</title>
      <link>https://snail007.goproxyauth.com/goproxy/page/about/</link>
      <pubDate>Fri, 14 Jun 2019 13:22:19 +0800</pubDate>
      
      <guid>https://snail007.goproxyauth.com/goproxy/page/about/</guid>
      <description>It&amp;rsquo;s YOU!</description>
    </item>
    
  </channel>
</rss>