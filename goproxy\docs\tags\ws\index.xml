<?xml version="1.0" encoding="utf-8" standalone="yes" ?>
<rss version="2.0" xmlns:atom="http://www.w3.org/2005/Atom">
  <channel>
    <title>ws on GOPROXY IN ACTION</title>
    <link>https://snail007.goproxyauth.com/goproxy/tags/ws/</link>
    <description>Recent content in ws on GOPROXY IN ACTION</description>
    <generator>Hugo -- gohugo.io</generator>
    <language>zh-CN</language>
    <lastBuildDate>Fri, 05 Jul 2019 08:20:48 +0800</lastBuildDate>
    
	<atom:link href="https://snail007.goproxyauth.com/goproxy/tags/ws/index.xml" rel="self" type="application/rss+xml" />
    
    
    <item>
      <title>域名解析 域名控制台&#43;极客&#43;cloudflare 的配置方法</title>
      <link>https://snail007.goproxyauth.com/goproxy/posts/domain-cf/</link>
      <pubDate>Fri, 05 Jul 2019 08:20:48 +0800</pubDate>
      
      <guid>https://snail007.goproxyauth.com/goproxy/posts/domain-cf/</guid>
      <description>准备工作 本文综合的介绍了cloudflare注册使用，从域名解析到回源，完成了一个二级域使用cloudflare的CDN加速的配置过程。 首先</description>
    </item>
    
    <item>
      <title>代理之套用CDN</title>
      <link>https://snail007.goproxyauth.com/goproxy/posts/http_cdn_ws/</link>
      <pubDate>Fri, 14 Jun 2019 16:25:06 +0800</pubDate>
      
      <guid>https://snail007.goproxyauth.com/goproxy/posts/http_cdn_ws/</guid>
      <description>goproxy代理之套用CDN 准备 在VPS上下载对应平台的执行文件，这里以Linux为例。 在电脑上下载对应平台的执行文件，这里以Window</description>
    </item>
    
    <item>
      <title>Categories</title>
      <link>https://snail007.goproxyauth.com/goproxy/page/categories/</link>
      <pubDate>Fri, 14 Jun 2019 15:29:47 +0800</pubDate>
      
      <guid>https://snail007.goproxyauth.com/goproxy/page/categories/</guid>
      <description>不用看这里，没什么内容，仅仅为了定义目录和tags。</description>
    </item>
    
  </channel>
</rss>