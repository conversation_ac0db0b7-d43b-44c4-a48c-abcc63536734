# GoProxy负载均衡配置文件
# 简洁配置，以够用为核心

# 服务配置
server:
  port: 7890                    # 统一HTTP代理入口端口
  timeout: 30                   # 请求超时时间（秒）
  max_connections: 1000         # 最大并发连接数

# YQProxy API配置
yqproxy:
  api_url: "http://yqproxy:5555"  # YQProxy API地址
  api_key: "yqproxy888"           # API密钥
  timeout: 10                     # API请求超时时间（秒）

# 代理池配置
proxy_pool:
  update_interval: 300            # 代理列表更新间隔（秒），默认5分钟
  protocols:                      # 支持的协议及每个协议获取的代理数量
    http: 50
    socks4: 30
    socks5: 30
  load_balance_mode: "random"     # 负载均衡模式: random(随机) | round_robin(轮询)
  health_check: true              # 是否启用代理健康检查
  health_check_interval: 600      # 健康检查间隔（秒），默认10分钟

# 日志配置
logging:
  level: "info"                   # 日志级别: debug | info | warn | error
  file: "/app/logs/proxy.log"     # 日志文件路径
  max_size: 100                   # 日志文件最大大小（MB）
  max_backups: 5                  # 保留的日志文件数量

# 监控配置
monitoring:
  enabled: true                   # 是否启用监控
  metrics_port: 8080              # 监控指标端口
  health_endpoint: "/health"      # 健康检查端点
