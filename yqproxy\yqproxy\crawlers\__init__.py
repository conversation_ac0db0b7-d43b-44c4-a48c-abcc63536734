import importlib
import inspect
import pkgutil

from yqproxy.crawlers.base import BaseCrawler


# load classes subclass of BaseCrawler (包括子包中的模块)
classes = []

def scan_package(package_path, package_name):
    """递归扫描包和子包中的模块"""
    for loader, name, is_pkg in pkgutil.walk_packages(package_path, package_name + '.'):
        try:
            module = importlib.import_module(name)
            for attr_name, value in inspect.getmembers(module):
                globals()[attr_name] = value
                if inspect.isclass(value) and issubclass(value, BaseCrawler) and value is not BaseCrawler \
                        and not getattr(value, 'ignore', False):
                    classes.append(value)
        except Exception as e:
            # 忽略导入错误，继续处理其他模块
            pass

# 扫描当前包
scan_package(__path__, __name__)
__all__ = __ALL__ = classes