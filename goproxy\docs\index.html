<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
<meta name="pinterest" content="nopin">
<meta name="viewport" content="width=device-width,minimum-scale=1,initial-scale=1">
<meta name="generator" content="Hugo 0.56.3" />

<link rel="alternate" type="application/rss+xml" title="RSS" href="https://snail007.goproxyauth.com/goproxy/index.xml">

<link rel="canonical" href="https://snail007.goproxyauth.com/goproxy/">


    <link href="//maxcdn.bootstrapcdn.com/font-awesome/4.5.0/css/font-awesome.min.css" rel="stylesheet">
    <link rel="stylesheet" href="//maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css">
    <link rel="stylesheet" href="//cdnjs.cloudflare.com/ajax/libs/highlight.js/8.4/styles/solarized_dark.min.css">
    <title>GOPROXY IN ACTION</title>
    
    <link href="https://snail007.goproxyauth.com/goproxy/css/styles.css" rel="stylesheet">
    

  </head>

  <body>
    
    
    

    <header class="l-header">
      <nav class="navbar navbar-default">
        <div class="container">
          <div class="navbar-header">
            <button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#navbar" aria-expanded="false">
              <span class="sr-only">Toggle navigation</span>
              <span class="icon-bar"></span>
              <span class="icon-bar"></span>
              <span class="icon-bar"></span>
            </button>
            <a class="navbar-brand" href="https://snail007.goproxyauth.com/goproxy/">GOPROXY IN ACTION</a>
          </div>

          
          <div id="navbar" class="collapse navbar-collapse">
            
            <ul class="nav navbar-nav navbar-right">
              
              
              <li><a href="https://github.com/snail007/goproxy/releases/">下载</a></li>
              
              
              
              <li><a href="https://snail007.goproxyauth.com/goproxy/categories/goproxy%E6%89%8B%E5%86%8C/">实战</a></li>
              
              
              
              <li><a href="https://snail007.goproxyauth.com/goproxy/manual/zh/#/">手册</a></li>
              
              
              
              <li><a href="/goproxy/about/">关于</a></li>
              
              
            </ul>
            
          </div>
          

        </div>
      </nav>
    </header>

    <main>
      <div class="container">
        
<div class="row">
  <div class="col-md-8">

    

    <ul class="p-articles">
      
      <li><article class="li">
  <header>
    <ul class="p-facts">
      <li><i class="fa fa-calendar" aria-hidden="true"></i><time datetime="2019-07-04T11:00:50JST">Jul 4, 2019</time></li>
      <li><i class="fa fa-bookmark" aria-hidden="true"></i><a href="https://snail007.goproxyauth.com/goproxy/usage/">usage</a></li>
      
    </ul>
    <h2 class="title"><a href="https://snail007.goproxyauth.com/goproxy/usage/first/">手册前言（使用必读）</a></h2>
  </header>

  
  <div class="summary">前言 GOPROXY使用必读 手册和项目中使用了一部分的名词，它们本身的含义和在goproxy的专有含义是不一样的，所以前言中对这些名词做了解释</div>

  
  <footer>
    <a href="https://snail007.goproxyauth.com/goproxy/usage/first/" title="手册前言（使用必读）">Read More…</a>
  </footer>
  
</article>
</li>
      
      <li><article class="li">
  <header>
    <ul class="p-facts">
      <li><i class="fa fa-calendar" aria-hidden="true"></i><time datetime="2019-06-26T13:19:15JST">Jun 26, 2019</time></li>
      <li><i class="fa fa-bookmark" aria-hidden="true"></i><a href="https://snail007.goproxyauth.com/goproxy/page/">page</a></li>
      
    </ul>
    <h2 class="title"><a href="https://snail007.goproxyauth.com/goproxy/page/free_vs_commercial/">GOPROXY免费版VS商业版</a></h2>
  </header>

  
  <div class="summary">目前，GOPROXY 和 SDK 提供免费版和商业版，功能对比如下。 免费版 商业版 TCP转发 √ √ UDP转发 √ √ HTTP(S)代理 √ √ Socks5代理 √ √</div>

  
  <footer>
    <a href="https://snail007.goproxyauth.com/goproxy/page/free_vs_commercial/" title="GOPROXY免费版VS商业版">Read More…</a>
  </footer>
  
</article>
</li>
      
      <li><article class="li">
  <header>
    <ul class="p-facts">
      <li><i class="fa fa-calendar" aria-hidden="true"></i><time datetime="2019-06-14T16:29:13JST">Jun 14, 2019</time></li>
      <li><i class="fa fa-bookmark" aria-hidden="true"></i><a href="https://snail007.goproxyauth.com/goproxy/page/">page</a></li>
      
    </ul>
    <h2 class="title"><a href="https://snail007.goproxyauth.com/goproxy/page/faq/goproxy%E5%B8%B8%E8%A7%81%E9%97%AE%E9%A2%98%E8%A7%A3%E7%AD%94/">常见问题解答</a></h2>
  </header>

  
  <div class="summary">问：http代理支持https网站吗？ 答：goproxy的http代理同时支持http和https网站。 问：socks5代理支持UDP吗？ 答</div>

  
  <footer>
    <a href="https://snail007.goproxyauth.com/goproxy/page/faq/goproxy%E5%B8%B8%E8%A7%81%E9%97%AE%E9%A2%98%E8%A7%A3%E7%AD%94/" title="常见问题解答">Read More…</a>
  </footer>
  
</article>
</li>
      
      <li><article class="li">
  <header>
    <ul class="p-facts">
      <li><i class="fa fa-calendar" aria-hidden="true"></i><time datetime="2019-07-05T08:20:48JST">Jul 5, 2019</time></li>
      <li><i class="fa fa-bookmark" aria-hidden="true"></i><a href="https://snail007.goproxyauth.com/goproxy/posts/">posts</a></li>
      
    </ul>
    <h2 class="title"><a href="https://snail007.goproxyauth.com/goproxy/posts/domain-cf/">域名解析 域名控制台&#43;极客&#43;cloudflare 的配置方法</a></h2>
  </header>

  
  <div class="summary">准备工作 本文综合的介绍了cloudflare注册使用，从域名解析到回源，完成了一个二级域使用cloudflare的CDN加速的配置过程。 首先</div>

  
  <footer>
    <a href="https://snail007.goproxyauth.com/goproxy/posts/domain-cf/" title="域名解析 域名控制台&#43;极客&#43;cloudflare 的配置方法">Read More…</a>
  </footer>
  
</article>
</li>
      
      <li><article class="li">
  <header>
    <ul class="p-facts">
      <li><i class="fa fa-calendar" aria-hidden="true"></i><time datetime="2019-07-04T17:54:31JST">Jul 4, 2019</time></li>
      <li><i class="fa fa-bookmark" aria-hidden="true"></i><a href="https://snail007.goproxyauth.com/goproxy/posts/">posts</a></li>
      
    </ul>
    <h2 class="title"><a href="https://snail007.goproxyauth.com/goproxy/posts/windows-global-proxy-using-dns/">在windows下实现基于dns的自动全局代理</a></h2>
  </header>

  
  <div class="summary">在windows下实现基于dns的自动全局代理。 本方法需要监听本地80和443端口，所以如果你有程序占用了这两个端口，请关闭相应的程序。 本文</div>

  
  <footer>
    <a href="https://snail007.goproxyauth.com/goproxy/posts/windows-global-proxy-using-dns/" title="在windows下实现基于dns的自动全局代理">Read More…</a>
  </footer>
  
</article>
</li>
      
      <li><article class="li">
  <header>
    <ul class="p-facts">
      <li><i class="fa fa-calendar" aria-hidden="true"></i><time datetime="2019-07-04T11:00:50JST">Jul 4, 2019</time></li>
      <li><i class="fa fa-bookmark" aria-hidden="true"></i><a href="https://snail007.goproxyauth.com/goproxy/usage/">usage</a></li>
      
    </ul>
    <h2 class="title"><a href="https://snail007.goproxyauth.com/goproxy/usage/tcp/">TCP代理</a></h2>
  </header>

  
  <div class="summary">工作原理 tcp代理，也称为tcp端口转发，工作原理就是，把proxy监听端口进来的tcp连接的数据原样转发到上级监听的一个端口。 上级可以是p</div>

  
  <footer>
    <a href="https://snail007.goproxyauth.com/goproxy/usage/tcp/" title="TCP代理">Read More…</a>
  </footer>
  
</article>
</li>
      
      <li><article class="li">
  <header>
    <ul class="p-facts">
      <li><i class="fa fa-calendar" aria-hidden="true"></i><time datetime="2019-06-28T17:37:30JST">Jun 28, 2019</time></li>
      <li><i class="fa fa-bookmark" aria-hidden="true"></i><a href="https://snail007.goproxyauth.com/goproxy/posts/">posts</a></li>
      
    </ul>
    <h2 class="title"><a href="https://snail007.goproxyauth.com/goproxy/posts/cloudflare/">Cloudflare使用简明教程</a></h2>
  </header>

  
  <div class="summary">前言 GOPROXY底层传输协议支持ws，可以利用cdn加速，使用cdn需要域名解析知识以及cdn的一些知识，那么很多小伙伴对cdn不是很熟悉</div>

  
  <footer>
    <a href="https://snail007.goproxyauth.com/goproxy/posts/cloudflare/" title="Cloudflare使用简明教程">Read More…</a>
  </footer>
  
</article>
</li>
      
      <li><article class="li">
  <header>
    <ul class="p-facts">
      <li><i class="fa fa-calendar" aria-hidden="true"></i><time datetime="2019-06-28T17:02:57JST">Jun 28, 2019</time></li>
      <li><i class="fa fa-bookmark" aria-hidden="true"></i><a href="https://snail007.goproxyauth.com/goproxy/posts/">posts</a></li>
      
    </ul>
    <h2 class="title"><a href="https://snail007.goproxyauth.com/goproxy/posts/http-nat-cdn/">内网穿透也能用CDN？？？！</a></h2>
  </header>

  
  <div class="summary">内网穿透之套CDN 好处就不用说了吧，两个字稳！ 假如在windows上配置了可以访问的连接 http://127.0.0.1:801/abc/index.php , 如何能使用VPS+CDN做内网穿透呢？ 配置如下：</div>

  
  <footer>
    <a href="https://snail007.goproxyauth.com/goproxy/posts/http-nat-cdn/" title="内网穿透也能用CDN？？？！">Read More…</a>
  </footer>
  
</article>
</li>
      
      <li><article class="li">
  <header>
    <ul class="p-facts">
      <li><i class="fa fa-calendar" aria-hidden="true"></i><time datetime="2019-06-26T13:19:15JST">Jun 26, 2019</time></li>
      <li><i class="fa fa-bookmark" aria-hidden="true"></i><a href="https://snail007.goproxyauth.com/goproxy/page/">page</a></li>
      
    </ul>
    <h2 class="title"><a href="https://snail007.goproxyauth.com/goproxy/page/free_vs_commercial_en/">GOPROXY Free Version VS Commercial Version</a></h2>
  </header>

  
  <div class="summary">Currently, GOPROXY and SDK are available in both free and commercial versions. Function Free |Commercial TCP forwarding √ √ UDP forwarding √ √ HTTP(S)Agent √ √ Socks5 Agent √ √ Socks5 proxy UDP support √ √ SPS Integrated Agent √ √ SPS superior certification √ √ Intranet penetration √ √ DNS proxy, anti-pollution, independent DNS service √ √ HTTP(S) proxy authentication, authentication API, load balancing × √ SOCKS5 proxy authentication,</div>

  
  <footer>
    <a href="https://snail007.goproxyauth.com/goproxy/page/free_vs_commercial_en/" title="GOPROXY Free Version VS Commercial Version">Read More…</a>
  </footer>
  
</article>
</li>
      
      <li><article class="li">
  <header>
    <ul class="p-facts">
      <li><i class="fa fa-calendar" aria-hidden="true"></i><time datetime="2019-06-14T16:25:06JST">Jun 14, 2019</time></li>
      <li><i class="fa fa-bookmark" aria-hidden="true"></i><a href="https://snail007.goproxyauth.com/goproxy/posts/">posts</a></li>
      
    </ul>
    <h2 class="title"><a href="https://snail007.goproxyauth.com/goproxy/posts/http_cdn_ws/">代理之套用CDN</a></h2>
  </header>

  
  <div class="summary">goproxy代理之套用CDN 准备 在VPS上下载对应平台的执行文件，这里以Linux为例。 在电脑上下载对应平台的执行文件，这里以Window</div>

  
  <footer>
    <a href="https://snail007.goproxyauth.com/goproxy/posts/http_cdn_ws/" title="代理之套用CDN">Read More…</a>
  </footer>
  
</article>
</li>
      
    </ul>

    
<nav>
  <ul class="pager">

    
    <li class="disabled"><a href="#">Previous</a></li>
    

    
    <li><a href="/goproxy/page/2/">Next</a></li>
    

  </ul>
</nav>



  </div>
  <div class="col-md-4">
    
<aside class="l-sidebar">

  <section class="panel panel-default">
    <div class="panel-heading">
      <div class="panel-title">LATESTS</div>
    </div>
    <div class="list-group">
      
      <a href="https://snail007.goproxyauth.com/goproxy/usage/first/" class="list-group-item">手册前言（使用必读）</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/page/free_vs_commercial/" class="list-group-item">GOPROXY免费版VS商业版</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/page/faq/goproxy%E5%B8%B8%E8%A7%81%E9%97%AE%E9%A2%98%E8%A7%A3%E7%AD%94/" class="list-group-item">常见问题解答</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/posts/domain-cf/" class="list-group-item">域名解析 域名控制台&#43;极客&#43;cloudflare 的配置方法</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/posts/windows-global-proxy-using-dns/" class="list-group-item">在windows下实现基于dns的自动全局代理</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/usage/tcp/" class="list-group-item">TCP代理</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/posts/cloudflare/" class="list-group-item">Cloudflare使用简明教程</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/posts/http-nat-cdn/" class="list-group-item">内网穿透也能用CDN？？？！</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/page/free_vs_commercial_en/" class="list-group-item">GOPROXY Free Version VS Commercial Version</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/posts/http_cdn_ws/" class="list-group-item">代理之套用CDN</a>
      
    </div>
  </section>

  
  <section class="panel panel-default">
    <div class="panel-heading">
      <div class="panel-title">CATEGORY</div>
    </div>
    <div class="list-group">
      
      <a href="https://snail007.goproxyauth.com/goproxy/categories/%E9%BB%98%E8%AE%A4%E5%88%86%E7%B1%BB" class="list-group-item">默认分类</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/categories/goproxy%E6%89%8B%E5%86%8C" class="list-group-item">goproxy手册</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/categories/%E6%9E%B6%E6%9E%84%E8%A7%A3%E8%AF%B4" class="list-group-item">架构解说</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/categories/%E7%BB%86%E8%AF%B4%E5%B1%82%E7%BA%A7" class="list-group-item">细说层级</a>
      
    </div>
  </section>
  
  <section class="panel panel-default">
    <div class="panel-heading">
      <div class="panel-title">TAG</div>
    </div>
    <div class="list-group">
      
      <a href="https://snail007.goproxyauth.com/goproxy/tags/cdn" class="list-group-item">cdn</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/tags/ws" class="list-group-item">ws</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/tags/cloudflare" class="list-group-item">cloudflare</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/tags/%E5%95%86%E4%B8%9A%E7%89%88" class="list-group-item">商业版</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/tags/commercial" class="list-group-item">commercial</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/tags/domain" class="list-group-item">domain</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/tags/tcp" class="list-group-item">tcp</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/tags/%E5%85%A8%E5%B1%80%E4%BB%A3%E7%90%86" class="list-group-item">全局代理</a>
      
      <a href="https://snail007.goproxyauth.com/goproxy/tags/%E5%86%85%E7%BD%91%E7%A9%BF%E9%80%8F" class="list-group-item">内网穿透</a>
      
    </div>
  </section>
  

</aside>


  </div>
</div>

      </div>
    </main>

    <footer class="l-footer">
      <div class="container">
        <p><span class="h-logo">&copy; GOPROXY IN ACTION</span></p>
      </div>
    </footer>

    <script src="//code.jquery.com/jquery-3.1.1.min.js"></script>
    <script src="//maxcdn.bootstrapcdn.com/bootstrap/3.3.7/js/bootstrap.min.js"></script>
    <script src="//cdnjs.cloudflare.com/ajax/libs/highlight.js/8.4/highlight.min.js"></script>
    <script>hljs.initHighlightingOnLoad();</script>
    <script>
    $(function(){
      $("img[css]").each(function(){
        $(this).attr("style",$(this).attr("css"));
      });
    });
    </script>
  </body>
</html>

