# 协议感知代理池系统 v2.0

一个高性能的代理池管理系统，支持HTTP、HTTPS、SOCKS4、SOCKS5四种协议的自动检测、分类存储和智能路由。

> **⚠️ v2.0.0 破坏性变更**: `get_random_proxy` 现在必须指定协议参数，且只返回满分代理。详见 [CHANGELOG](CHANGELOG.md)。

## 🚀 核心特性

- **协议感知**: 自动检测和分类存储不同协议的代理
- **高性能**: 协议检测 491万次/秒，代理创建 22万次/秒
- **智能路由**: 根据协议类型智能路由和负载均衡
- **严格验证**: 拒绝无法确定协议的代理，保证数据质量
- **RESTful API**: 完整的API接口支持
- **多种部署**: 支持直接启动、Docker、Systemd部署

## 📦 快速安装

```bash
# 克隆项目
git clone <repository-url>
cd yqproxy

# 安装依赖
pip install -r requirements.txt

# 启动Redis服务
redis-server

# 部署系统
python deploy.py
```

## 🎯 快速启动

```bash
# 方式1: 直接启动
./start.sh

# 方式2: Docker部署
docker-compose up -d

# 方式3: 手动启动API服务器
python -m yqproxy.workers.server
```

## 📖 API使用

### ⚠️ 重要变更 (v2.0.0)
从 v2.0.0 开始，`/random` 接口必须指定 `protocol` 参数，且只返回满分（score=100）的代理。

```bash
# 获取随机代理（v2.0.0+ 必须指定协议）
curl http://localhost:5555/random?protocol=http
curl http://localhost:5555/random?protocol=socks5

# 获取HTTP代理
curl http://localhost:5555/protocol/http

# 获取SOCKS5代理
curl http://localhost:5555/protocol/socks5

# 批量获取代理
curl http://localhost:5555/batch?count=10

# 批量获取指定协议的代理
curl http://localhost:5555/batch?count=10&protocol=http

# 查看系统统计
curl http://localhost:5555/stats

# 健康检查
curl http://localhost:5555/health
```

### Python SDK 使用示例

```python
from yqproxy.services.proxy_manager import ProxyManager

# v2.0.0+ 使用方式（必须指定协议）
proxy = proxy_manager.get_random_proxy(protocol='http')
if proxy:
    print(f"获取到高质量代理: {proxy}")
else:
    print("无可用的满分代理")
```

## 🏗️ 系统架构

```
代理源 → 爬虫 → 协议检测 → 分表存储 → 协议路由 → API接口
  ↓        ↓        ↓          ↓          ↓        ↓
HTTP    推断    验证格式    proxies:http   智能    /random
SOCKS4  协议    拒绝无效    proxies:socks4 负载    /protocol/http
SOCKS5  类型    严格验证    proxies:socks5 均衡    /batch
```

## 📚 文档

- [API参考](API_REFERENCE.md) - 完整的API文档和使用说明
- [更新日志](CHANGELOG.md) - 版本更新和破坏性变更记录
- [迁移指南](API_REFERENCE.md#迁移指南) - v1.x 到 v2.0 迁移说明

## 🔧 配置

主要配置项在 `.env` 文件中：

```bash
# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# API配置
API_HOST=0.0.0.0
API_PORT=5555
API_KEY=your_api_key

# 测试配置
TEST_URL=http://httpbin.org/ip
TEST_TIMEOUT=10
```

## 📊 性能指标

| 组件 | 性能指标 |
|------|----------|
| 协议检测 | 491万次/秒 |
| 代理创建 | 22万次/秒 |
| Redis操作 | 1万次/秒 |
| API响应 | <100ms |

## 🛠️ 开发

```bash
# 安装开发依赖
pip install -r requirements.txt

# 运行代理获取器
python -m yqproxy.workers.getter

# 运行代理测试器
python -m yqproxy.workers.tester

# 启动API服务器
python -m yqproxy.workers.server
```

## 📝 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📞 支持

如有问题，请提交 Issue 或查看文档。