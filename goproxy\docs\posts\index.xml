<?xml version="1.0" encoding="utf-8" standalone="yes" ?>
<rss version="2.0" xmlns:atom="http://www.w3.org/2005/Atom">
  <channel>
    <title>Posts on GOPROXY IN ACTION</title>
    <link>https://snail007.goproxyauth.com/goproxy/posts/</link>
    <description>Recent content in Posts on GOPROXY IN ACTION</description>
    <generator>Hugo -- gohugo.io</generator>
    <language>zh-CN</language>
    <lastBuildDate>Fri, 05 Jul 2019 08:20:48 +0800</lastBuildDate>
    
	<atom:link href="https://snail007.goproxyauth.com/goproxy/posts/index.xml" rel="self" type="application/rss+xml" />
    
    
    <item>
      <title>域名解析 域名控制台&#43;极客&#43;cloudflare 的配置方法</title>
      <link>https://snail007.goproxyauth.com/goproxy/posts/domain-cf/</link>
      <pubDate>Fri, 05 Jul 2019 08:20:48 +0800</pubDate>
      
      <guid>https://snail007.goproxyauth.com/goproxy/posts/domain-cf/</guid>
      <description>准备工作 本文综合的介绍了cloudflare注册使用，从域名解析到回源，完成了一个二级域使用cloudflare的CDN加速的配置过程。 首先</description>
    </item>
    
    <item>
      <title>在windows下实现基于dns的自动全局代理</title>
      <link>https://snail007.goproxyauth.com/goproxy/posts/windows-global-proxy-using-dns/</link>
      <pubDate>Thu, 04 Jul 2019 17:54:31 +0800</pubDate>
      
      <guid>https://snail007.goproxyauth.com/goproxy/posts/windows-global-proxy-using-dns/</guid>
      <description>在windows下实现基于dns的自动全局代理。 本方法需要监听本地80和443端口，所以如果你有程序占用了这两个端口，请关闭相应的程序。 本文</description>
    </item>
    
    <item>
      <title>Cloudflare使用简明教程</title>
      <link>https://snail007.goproxyauth.com/goproxy/posts/cloudflare/</link>
      <pubDate>Fri, 28 Jun 2019 17:37:30 +0800</pubDate>
      
      <guid>https://snail007.goproxyauth.com/goproxy/posts/cloudflare/</guid>
      <description>前言 GOPROXY底层传输协议支持ws，可以利用cdn加速，使用cdn需要域名解析知识以及cdn的一些知识，那么很多小伙伴对cdn不是很熟悉</description>
    </item>
    
    <item>
      <title>内网穿透也能用CDN？？？！</title>
      <link>https://snail007.goproxyauth.com/goproxy/posts/http-nat-cdn/</link>
      <pubDate>Fri, 28 Jun 2019 17:02:57 +0800</pubDate>
      
      <guid>https://snail007.goproxyauth.com/goproxy/posts/http-nat-cdn/</guid>
      <description>内网穿透之套CDN 好处就不用说了吧，两个字稳！ 假如在windows上配置了可以访问的连接 http://127.0.0.1:801/abc/index.php , 如何能使用VPS+CDN做内网穿透呢？ 配置如下：</description>
    </item>
    
    <item>
      <title>代理之套用CDN</title>
      <link>https://snail007.goproxyauth.com/goproxy/posts/http_cdn_ws/</link>
      <pubDate>Fri, 14 Jun 2019 16:25:06 +0800</pubDate>
      
      <guid>https://snail007.goproxyauth.com/goproxy/posts/http_cdn_ws/</guid>
      <description>goproxy代理之套用CDN 准备 在VPS上下载对应平台的执行文件，这里以Linux为例。 在电脑上下载对应平台的执行文件，这里以Window</description>
    </item>
    
  </channel>
</rss>