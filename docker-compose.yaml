version: '3.8'

services:
  # Redis服务 - 存储代理池数据
  redis:
    image: redis:7-alpine
    container_name: proxy_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    networks:
      - proxy_network

  # YQProxy代理池服务
  yqproxy:
    build: ./yqproxy
    container_name: yqproxy
    restart: unless-stopped
    ports:
      - "5555:5555"
    volumes:
      - ./yqproxy/logs:/app/logs
      - ./yqproxy/.env:/app/.env:ro
    env_file:
      - ./yqproxy/.env
    environment:
      - API_HOST=0.0.0.0
      - REDIS_HOST=redis
    depends_on:
      - redis
    networks:
      - proxy_network

  # GoProxy负载均衡代理服务
  goproxy:
    build: 
      context: ./goproxy
      dockerfile: Dockerfile.loadbalancer
    container_name: goproxy_lb
    restart: unless-stopped
    ports:
      - "7890:7890"  # 统一HTTP代理入口
    volumes:
      - ./goproxy/config:/app/config:ro
    environment:
      - YQPROXY_API_URL=http://yqproxy:5555
      - YQPROXY_API_KEY=yqproxy888
      - PROXY_PORT=7890
      - UPDATE_INTERVAL=300  # 5分钟更新一次代理列表
    depends_on:
      - yqproxy
    networks:
      - proxy_network

volumes:
  redis_data:
    driver: local

networks:
  proxy_network:
    driver: bridge
