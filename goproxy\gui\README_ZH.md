# Proxy-GUI
基于proxy的各平台SDK,作者和众多热心人士开发了各平台的GUI版本的proxy,下面分平台介绍.  

## Windows

- 官方java版本,项目主页:[goproxy-jui](https://github.com/snail007/goproxy-jui)
- 官方c#版本,项目主页:[gptool-csharp](https://github.com/snail007/gptool-csharp)

## Linux

- 官方java版本,项目主页:[goproxy-jui](https://github.com/snail007/goproxy-jui)

## MacOS

- Coming Soon ...

## Android

- 官方goproxy的ss插件，下载地址：[ss-plugin-goproxy](https://github.com/snail007/goproxy-ss-plugin-android)

## IOS

- Coming Soon ...

## 跨平台

- proxy-web,一个跨平台的web UI版本,项目主页:[proxy-web](https://github.com/yincongcyincong/proxy-web)