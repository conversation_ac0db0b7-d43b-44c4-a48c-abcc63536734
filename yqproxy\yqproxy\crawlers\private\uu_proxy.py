# encoding: utf-8
import urllib.parse
from yqproxy.crawlers.base import BaseCrawler
from yqproxy.models import Proxy
from yqproxy.utils import get_logger

# UU Proxy API配置
API_URL = 'http://uu-proxy.com/api/get_proxies'
TOKEN_ID = 'ZD48ZCL4JC'
SCHEMES = ['http', 'socks4', 'socks5']


class UUProxyCrawler(BaseCrawler):
    """UU代理爬虫 - 支持HTTP、SOCKS4、SOCKS5"""
    
    def __init__(self):
        super().__init__()
        self.logger = get_logger(self.__class__.__name__)

    def _get_proxies_url(self, scheme):
        """构建指定协议的代理请求URL"""
        params = {
            'id': TOKEN_ID,
            'size': 50,
            'schemes': scheme,
            'support_https': 'true',
            'restime_within_ms': 10000,
            'format': 'txt2_2'
        }
        return f"{API_URL}?{urllib.parse.urlencode(params)}"

    def crawl(self):
        """爬取所有协议的代理"""
        for scheme in SCHEMES:
            try:
                # 使用基类的fetch方法，获得更好的错误处理和重试机制
                url = self._get_proxies_url(scheme)
                proxy_text = self.fetch(url)
                if not proxy_text:
                    continue
                
                for line in proxy_text.strip().split('\n'):
                    line = line.strip()
                    if not line:
                        continue
                    
                    try:
                        proxy = Proxy.from_string(line)
                        yield proxy
                    except Exception as e:
                        self.logger.debug(f'解析代理失败 [{scheme}] {line}: {e}')
                
            except Exception as e:
                self.logger.error(f'{scheme} 协议爬取失败: {e}')
