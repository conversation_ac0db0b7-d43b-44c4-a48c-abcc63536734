# 代理池负载均衡系统

基于YQProxy代理池和GoProxy的统一HTTP代理入口，提供类似mihomo的7890端口功能，实现代理池的负载均衡。

## 🚀 快速开始

### 1. 准备配置文件

```bash
# 复制YQProxy配置文件
cp yqproxy/.env.example yqproxy/.env

# 编辑配置文件，设置Redis连接信息
vim yqproxy/.env
```

### 2. 启动服务

```bash
# 给启动脚本执行权限
chmod +x start.sh

# 启动所有服务
./start.sh start
```

### 3. 使用代理

设置你的应用程序使用以下代理：
- **HTTP代理**: `localhost:7890`
- **HTTPS代理**: `localhost:7890`

## 📋 服务架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     Redis       │    │    YQProxy      │    │    GoProxy      │
│   (代理存储)     │◄───│   (代理池)      │◄───│  (负载均衡器)    │
│   Port: 6379    │    │   Port: 5555    │    │   Port: 7890    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                       ▲
                                                       │
                                               ┌─────────────────┐
                                               │   客户端应用     │
                                               │ (设置代理7890)   │
                                               └─────────────────┘
```

## 🔧 配置说明

### Docker Compose 配置 (`docker-compose.yaml`)

- **Redis**: 代理数据存储，端口6379
- **YQProxy**: 代理池管理服务，端口5555
- **GoProxy**: 负载均衡代理服务，端口7890

### GoProxy 配置 (`goproxy/config/proxy.yaml`)

```yaml
# 核心配置项
server:
  port: 7890                    # 代理服务端口
  timeout: 30                   # 请求超时时间

yqproxy:
  api_url: "http://yqproxy:5555"  # YQProxy API地址
  api_key: "yqproxy888"           # API密钥

proxy_pool:
  update_interval: 300            # 代理更新间隔（5分钟）
  protocols:                      # 各协议代理数量
    http: 50
    socks4: 30
    socks5: 30
```

## 📊 工作原理

1. **代理获取**: GoProxy定时从YQProxy API获取满分代理
2. **协议支持**: 支持HTTP、SOCKS4、SOCKS5协议的代理
3. **负载均衡**: 随机选择可用代理进行请求转发
4. **自动更新**: 每5分钟自动更新代理列表
5. **故障转移**: 代理失败时自动切换到其他代理

## 🛠️ 管理命令

```bash
# 启动服务
./start.sh start

# 停止服务
./start.sh stop

# 重启服务
./start.sh restart

# 查看服务状态
./start.sh status

# 查看服务日志
./start.sh logs

# 手动操作Docker Compose
docker-compose up -d        # 启动
docker-compose down         # 停止
docker-compose ps           # 状态
docker-compose logs -f      # 日志
```

## 📈 监控和调试

### 查看YQProxy状态
```bash
curl http://localhost:5555/stats
curl http://localhost:5555/health
```

### 测试代理功能
```bash
# 测试HTTP代理
curl -x http://localhost:7890 http://httpbin.org/ip

# 测试HTTPS代理
curl -x http://localhost:7890 https://httpbin.org/ip
```

### 查看代理池信息
```bash
# 获取随机HTTP代理
curl "http://localhost:5555/random?protocol=http"

# 批量获取代理
curl "http://localhost:5555/batch?count=10&protocol=http"
```

## 🔍 故障排除

### 1. 服务启动失败
```bash
# 检查Docker服务
sudo systemctl status docker

# 检查端口占用
netstat -tlnp | grep -E "(6379|5555|7890)"

# 查看详细日志
docker-compose logs
```

### 2. 代理连接失败
```bash
# 检查YQProxy API
curl http://localhost:5555/health

# 检查代理池状态
curl http://localhost:5555/stats

# 查看GoProxy日志
docker-compose logs goproxy_lb
```

### 3. 代理数量不足
```bash
# 检查Redis中的代理数据
docker-compose exec redis redis-cli
> ZCARD proxies:http
> ZCARD proxies:socks4
> ZCARD proxies:socks5
```

## ⚙️ 高级配置

### 自定义代理更新频率
编辑 `docker-compose.yaml` 中的环境变量：
```yaml
environment:
  - UPDATE_INTERVAL=180  # 3分钟更新一次
```

### 调整代理获取数量
编辑 `goproxy/config/proxy.yaml`：
```yaml
proxy_pool:
  protocols:
    http: 100     # 增加HTTP代理数量
    socks4: 50
    socks5: 50
```

### 启用调试日志
```yaml
logging:
  level: "debug"  # 启用详细日志
```

## 📝 注意事项

1. **首次启动**: 需要等待YQProxy爬取和验证代理，可能需要几分钟
2. **代理质量**: 只使用满分(100分)的代理，确保连接质量
3. **更新频率**: 默认5分钟更新一次，可根据需要调整
4. **资源使用**: 建议至少2GB内存和2核CPU
5. **网络环境**: 确保能够访问代理源网站

## 🤝 支持

如有问题，请检查：
1. Docker和Docker Compose版本
2. 网络连接和防火墙设置
3. 配置文件格式和内容
4. 服务日志中的错误信息
