"""
代理数据模型
纯数据类，只依赖constants，无业务逻辑依赖
"""
import re
from dataclasses import dataclass
from typing import Optional, Dict, Any

from yqproxy.models.constants import (
    Protocol,
    SUPPORTED_PROTOCOLS,
    DEFAULT_PROTOCOL,
    PROXY_SCORE_INIT,
    PROXY_SCORE_MAX,
    PROXY_SCORE_MIN
)


@dataclass
class Proxy:
    """
    代理数据模型，纯数据，无业务逻辑
    """
    host: str
    port: int
    protocol: str = DEFAULT_PROTOCOL
    username: Optional[str] = None
    password: Optional[str] = None
    score: int = PROXY_SCORE_INIT
    
    def __post_init__(self):
        """初始化后的验证和标准化"""
        # 标准化协议
        if self.protocol not in SUPPORTED_PROTOCOLS:
            self.protocol = DEFAULT_PROTOCOL
        
        # 确保分数在有效范围内
        if self.score < PROXY_SCORE_MIN:
            self.score = PROXY_SCORE_MIN
        elif self.score > PROXY_SCORE_MAX:
            self.score = PROXY_SCORE_MAX
    
    @property
    def address(self) -> str:
        """返回代理地址 host:port"""
        return f"{self.host}:{self.port}"
    
    @property
    def url(self) -> str:
        """返回完整的代理URL"""
        if self.username and self.password:
            return f"{self.protocol}://{self.username}:{self.password}@{self.address}"
        elif self.username:
            return f"{self.protocol}://{self.username}@{self.address}"
        else:
            return f"{self.protocol}://{self.address}"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "host": self.host,
            "port": self.port,
            "protocol": self.protocol,
            "username": self.username,
            "password": self.password,
            "score": self.score,
            "address": self.address,
            "url": self.url
        }
    
    def to_requests_format(self) -> Dict[str, str]:
        """转换为requests库使用的代理格式"""
        proxy_url = self.url
        
        if self.protocol in [Protocol.HTTP, Protocol.HTTPS]:
            return {
                'http': proxy_url,
                'https': proxy_url
            }
        elif self.protocol in [Protocol.SOCKS4, Protocol.SOCKS5]:
            return {
                'http': proxy_url,
                'https': proxy_url
            }
        else:
            # 默认使用HTTP格式
            fallback_url = f"http://{self.address}"
            return {
                'http': fallback_url,
                'https': fallback_url
            }
    
    def is_valid(self) -> bool:
        """验证代理信息的基本有效性"""
        # 检查必需字段
        if not self.host or not self.port:
            return False
        
        # 检查端口范围
        if not (1 <= self.port <= 65535):
            return False
        
        # 检查host格式（简单验证）
        if not re.match(r'^[a-zA-Z0-9.-]+$', self.host):
            return False
        
        # 检查协议
        if self.protocol not in SUPPORTED_PROTOCOLS:
            return False
        
        return True
    
    def validate(self) -> bool:
        """验证代理有效性（兼容性方法，等同于is_valid）"""
        return self.is_valid()
    
    def get_normalized_string(self) -> str:
        """获取标准化的代理字符串表示
        格式: protocol://host:port 或 protocol://user:pass@host:port
        """
        if self.username and self.password:
            return f"{self.protocol}://{self.username}:{self.password}@{self.host}:{self.port}"
        elif self.username:
            return f"{self.protocol}://{self.username}@{self.host}:{self.port}"
        else:
            return f"{self.protocol}://{self.host}:{self.port}"
    
    def is_authenticated(self) -> bool:
        """检查代理是否需要认证"""
        return bool(self.username)
    
    def is_secure(self) -> bool:
        """检查代理是否使用安全协议"""
        from yqproxy.models.constants import SECURE_PROTOCOLS
        return self.protocol in SECURE_PROTOCOLS
    
    @classmethod
    def from_string(cls, proxy_string: str, default_protocol: str = DEFAULT_PROTOCOL) -> Optional['Proxy']:
        """
        从代理字符串创建Proxy对象（简化版本，无外部依赖）
        
        支持的格式：
        - host:port
        - protocol://host:port
        - protocol://username:password@host:port
        
        :param proxy_string: 代理字符串
        :param default_protocol: 默认协议
        :return: Proxy对象或None
        """
        if not proxy_string or not proxy_string.strip():
            return None
        
        proxy_string = proxy_string.strip()
        
        try:
            # 检测协议
            protocol = default_protocol
            if '://' in proxy_string:
                protocol_part, remainder = proxy_string.split('://', 1)
                if protocol_part.lower() in SUPPORTED_PROTOCOLS:
                    protocol = protocol_part.lower()
                    proxy_string = remainder
            
            # 解析认证信息
            username = None
            password = None
            if '@' in proxy_string:
                auth_part, address_part = proxy_string.rsplit('@', 1)
                if ':' in auth_part:
                    username, password = auth_part.split(':', 1)
                else:
                    username = auth_part
                proxy_string = address_part
            
            # 解析主机和端口
            if ':' not in proxy_string:
                return None
            
            host, port_str = proxy_string.rsplit(':', 1)
            
            try:
                port = int(port_str)
            except ValueError:
                return None
            
            # 创建代理对象
            return cls(
                host=host,
                port=port,
                protocol=protocol,
                username=username,
                password=password
            )
            
        except Exception:
            return None
    
    @classmethod
    def from_address(cls, address: str, protocol: str = DEFAULT_PROTOCOL, 
                     username: Optional[str] = None, password: Optional[str] = None,
                     score: int = PROXY_SCORE_INIT) -> Optional['Proxy']:
        """
        从地址字符串创建Proxy对象
        
        :param address: 地址字符串 host:port
        :param protocol: 协议类型
        :param username: 用户名
        :param password: 密码
        :param score: 分数
        :return: Proxy对象或None
        """
        if not address or ':' not in address:
            return None
        
        try:
            host, port_str = address.rsplit(':', 1)
            port = int(port_str)
            
            return cls(
                host=host,
                port=port,
                protocol=protocol,
                username=username,
                password=password,
                score=score
            )
        except (ValueError, AttributeError):
            return None
    
    def __str__(self) -> str:
        """字符串表示：host:port"""
        return self.address
    
    def __repr__(self) -> str:
        """开发者友好的字符串表示"""
        return (f'Proxy(host="{self.host}", port={self.port}, '
                f'protocol="{self.protocol}", score={self.score}, '
                f'authenticated={self.is_authenticated()})')
    
    def __eq__(self, other) -> bool:
        """比较两个代理是否相同"""
        if not isinstance(other, Proxy):
            return False
        return (self.host == other.host and 
                self.port == other.port and 
                self.protocol == other.protocol and
                self.username == other.username and
                self.password == other.password)
    
    def __hash__(self) -> int:
        """计算代理的哈希值，用于去重"""
        return hash((self.host, self.port, self.protocol, self.username, self.password))